package com.facishare.open.outer.oa.connector.common.api.params;

import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class AlertConfigArg extends CepArg{
    /**
     * 推送配置 true:开启 false 关闭
     */
    private Boolean alertConfig;


    /**
     * 推送消息提醒 包含：CRM待办、CRM提醒、CRM日程、CRM审批等类型
     *
     * @see com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum
     */
    private List<AlertTypeEnum> alertTypes;

    /**
     * 只有钉钉有
     * 待办推送的位置  0：工作通知 1：钉钉待办 2：都推送
     */
    private Integer todoType;


    /**
     * 只有钉钉有
     * 待办创建者ID，用户输入
     */
    private String todoCreator;


    /**
     * 只有钉钉有
     * 待办创建者UnionID，用户不需要输入
     */
    private String todoCreatorUnionId;
}
