package com.facishare.open.outer.oa.connector.common.api.enums;

import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 帐号绑定类型
 */
@Getter
@AllArgsConstructor
public enum CRMEmployeeFiledEnum implements Serializable {
    NAME ("name","系统名(昵称)",I18NStringEnum.s184),
    EMAIL("email","邮箱",I18NStringEnum.s185),
    PHONE ("phone","手机",I18NStringEnum.s186),
    EMPLOYEE_NUMBER ("employee_number","员工编号",I18NStringEnum.s187),
    //查询数据需要owner_department，insert 数据需要main_department
    MAIN_DEPARTMENT ("main_department","主属部门",I18NStringEnum.s188),
    OWNER_DEPARTMENT ("owner_department","归属部门",I18NStringEnum.s189),
    STATUS ("status","员工状态",I18NStringEnum.s190),
    USER_ID ("user_id","用户ID",I18NStringEnum.s191),
    LEADER("leader","汇报对象",I18NStringEnum.s192),
    ;

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 多语对象描述key
     */
    private final I18NStringEnum i18nKey;
    /**
     * 根据code获取枚举
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static CRMEmployeeFiledEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CRMEmployeeFiledEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
