package com.facishare.open.outer.oa.connector.common.api.info;

import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 企业试用情况
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EnterpriseTrialInfo implements Serializable {
    private String outEa;  //外部id
    private Boolean isTrial;  //最新的订单是否是试用订单
    private Timestamp beginTime;  //试用订单开始时间
    private Timestamp endTime;  //试用订单结束时间
    private String extend;  //拓展字段，1、这里取留资是否首次使用crm，字段：isFirstLand。2、是否已经留资,字段：isRetainInformation。
    private BindTypeEnum bindType;  //企业绑定类型
}
