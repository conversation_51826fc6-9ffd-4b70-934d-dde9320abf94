package com.facishare.open.outer.oa.connector.common.api.object;

import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.enums.CRMEmployeeFiledEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FieldTypeEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import lombok.Data;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * 钉钉员工扩展数据 - MongoDB
 * <AUTHOR>
 * @date 2024/1/22 15:46:25
 */
@Data
public class DingTalkEmployeeObject extends BaseOuterEmployeeObject implements Serializable {

    /** 钉钉职员ID **/
    @Property("userid")
    @SystemAnnotation(value = CRMEmployeeFiledEnum.USER_ID,i18n = I18NStringEnum.s196)
    private String userid;

    /**
     * 钉钉员工状态
     */
    @Property("status")
    private Integer status;

    /**
     * 钉钉员工姓名
     */
    @Property("name")
    @SystemAnnotation(value = CRMEmployeeFiledEnum.NAME,i18n = I18NStringEnum.s194)
    private String name;

    /**
     * 钉钉员工手机号
     */
    @Property("phone")
    @SystemAnnotation(value = CRMEmployeeFiledEnum.PHONE,i18n = I18NStringEnum.s186)
    private String phone;

    /**
     * 钉钉员工unionId
     */
    @Property("unionId")
    private String unionId;

    /**
     * 钉钉部门ID
     */
    @Property("deptId")
    @SystemAnnotation(value = CRMEmployeeFiledEnum.MAIN_DEPARTMENT, description = "取第一个部门作为主部门",outerOaFieldType = FieldTypeEnum.department,i18n = I18NStringEnum.s188)
    private Long deptId;

    /**
     * 钉钉部门名称
     */
    @Property("deptName")
    @Deprecated
    private String deptName;

    /**
     * 钉钉职员职位
     */
    private String position;

    /**
     * 钉钉职员工号
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.EMPLOYEE_NUMBER,description = "钉钉职员工号",outerOaFieldType = FieldTypeEnum.text,i18n = I18NStringEnum.s195)
    private String jobNumber;


    /**
     * 直属上级
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.LEADER,description = "直属上级",outerOaFieldType = FieldTypeEnum.employee,i18n = I18NStringEnum.s197)
    private String manager_userid;

    /**
     * 钉钉职员邮箱
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.EMAIL,description = "钉钉职员邮箱",outerOaFieldType = FieldTypeEnum.text,i18n = I18NStringEnum.s185)
    private String email;

    /**
     * 钉钉职员性别
     */
    private String sexType;

}