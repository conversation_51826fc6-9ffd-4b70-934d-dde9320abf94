package com.facishare.open.outer.oa.connector.common.api.result;

import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EnterpriseConfigAccountSyncTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.params.CepArg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class SettingsResult extends CepArg implements Serializable {
   /**
    * 账号绑定类型
    */
   private EnterpriseConfigAccountSyncTypeEnum syncTypeEnum;

   /**
    * 账号绑定规则
    */
   private BindTypeEnum bindTypeEnum;

   /**
    * 绑定规则
    */

   private SystemFieldMappingResult.ItemFieldMapping employeeBindRule;
   /**
    * 范围移除处理逻辑
    */
   private SettingAccountRulesModel.EmployeeRangeRemoveRule employeeRangeRemoveRule;

   /***
    * 外部人员离职，处理逻辑
    */
   private SettingAccountRulesModel.EmployeeLeaveRule employeeLeaveRule;

   /**
    * 人员字段映射
    */

   private List<SystemFieldMappingResult.ItemFieldMapping> employeeFieldMapping;


   /**
    * 部门字段映射，ISV才返回对应字段
    */

   private List<DeptFieldMapping> deptFieldMappings;

   /**
    * 绑定的字段设置
    */
   @Data
   public static class EmployeeBindRule implements Serializable {
      private static final long serialVersionUID = -1L;

      //crm人员字段值
      private String crmFiledValue;

      //外部系统人员字段值
      private String outFiledValue;
   }



   /**
    * 人员字符映射
    */
   @Data
   @AllArgsConstructor
   @NoArgsConstructor
   public static class EmployeeFieldMapping implements Serializable {
      private static final long serialVersionUID = -1L;
      private String outerFieldApiName;
      private String outerFieldLabel;
      private String crmFieldApiName;
      private String crmFieldLabel;
   }

   /**
    * 部门字符映射
    */
   @Data
   @AllArgsConstructor
   @NoArgsConstructor
   public static class DeptFieldMapping implements Serializable {
      private static final long serialVersionUID = -1L;
      private String sourceFieldApiName;
      private String sourceFieldLabel;
      private String destFieldApiName;
      private String destFieldLabel;
   }
}
