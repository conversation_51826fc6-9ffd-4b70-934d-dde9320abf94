package com.facishare.open.order.contacts.proxy.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.open.order.contacts.proxy.api.data.Crm112ObjectCustomer;
import com.facishare.open.order.contacts.proxy.api.data.CrmObjectCustomer;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.limiter.CrmRateLimiter;
import com.facishare.open.order.contacts.proxy.paas.dao.Crm112ObjectCustomerDao;
import com.facishare.open.order.contacts.proxy.paas.dao.CrmObjectCustomerDao;
import com.facishare.paas.dao.BaseDao;
import com.facishare.paas.model.PaasBean;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.facishare.webhook.common.util.Constant.ENTERPRISEID;
import static com.facishare.webhook.common.util.Constant.OPERATERID;


@Service
@Slf4j
public class CrmCustomerManager<T> {
    @Autowired
    private CrmObjectCustomerDao crmObjectCustomerDao;
    @Autowired
    private Crm112ObjectCustomerDao crm112ObjectCustomerDao;

    public Result<String> createObjCustomer(T customerObject) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }

        if(customerObject instanceof Crm112ObjectCustomer) {
            Crm112ObjectCustomer crm112ObjectCustomer = (Crm112ObjectCustomer) customerObject;
            crm112ObjectCustomer.set__xxx_enterpriseId(ENTERPRISEID);
            crm112ObjectCustomer.setOperatorId(OPERATERID);

            BaseDao.OptionInfo optionInfo = new BaseDao.OptionInfo();
            optionInfo.setIsDuplicateSearch(false);

            Map<String, Object> map = Maps.newHashMap();
            map.put("optionInfo", JSON.toJSON(optionInfo));

            PaasBean.Result<Crm112ObjectCustomer> paasResult = null;
            try {
                paasResult = crm112ObjectCustomerDao.insert(crm112ObjectCustomer, map);
            } catch (Exception e) {
                LogUtils.info("CrmManager.createObjCustomer error,message={}", e.getMessage());
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            LogUtils.info("CrmManager.createObjCustomer,paasResult={}",paasResult);

            if(ObjectUtils.isEmpty(paasResult.getObject_data()) || ObjectUtils.isEmpty(paasResult.getObject_data().get("_id"))) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            return Result.newSuccess((String) paasResult.getObject_data().get("_id"));
        } else {
            CrmObjectCustomer crmObjectCustomer = (CrmObjectCustomer) customerObject;
            crmObjectCustomer.set__xxx_enterpriseId(ENTERPRISEID);
            crmObjectCustomer.setOperatorId(OPERATERID);

            BaseDao.OptionInfo optionInfo = new BaseDao.OptionInfo();
            optionInfo.setIsDuplicateSearch(false);

            Map<String, Object> map = Maps.newHashMap();
            map.put("optionInfo", JSON.toJSON(optionInfo));

            PaasBean.Result<CrmObjectCustomer> paasResult = null;
            try {
                paasResult = crmObjectCustomerDao.insert(crmObjectCustomer, map);
            } catch (Exception e) {
                LogUtils.info("CrmManager.createObjCustomer error,message={}", e.getMessage());
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            LogUtils.info("CrmManager.createObjCustomer,paasResult={}",paasResult);

            if(ObjectUtils.isEmpty(paasResult.getObject_data()) || ObjectUtils.isEmpty(paasResult.getObject_data().get("_id"))) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            return Result.newSuccess((String) paasResult.getObject_data().get("_id"));
        }
    }
}
