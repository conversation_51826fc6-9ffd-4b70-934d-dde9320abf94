package com.facishare.open.order.contacts.proxy.aop;

import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.limiter.CrmRateLimiter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Component;

/**
 * 调用crm接口限速
 */
@Component
public class CrmRateLimiterAspect {
    public Object around(ProceedingJoinPoint point) throws Throwable {
        String fullClassName = point.getTarget().getClass().getName();
        String methodName = point.getSignature().getName();
        Object result = null;
        if (CrmRateLimiter.isAllowed(null)) {
            // 调用接口
            LogUtils.info("CrmRimiterAspect.around,{}.{}",
                    fullClassName, methodName);
            result = point.proceed();
        } else {
            // 处理超限情况
            LogUtils.info("CrmRimiterAspect.around,{}.{} frequent calls",
                    fullClassName, methodName);
            return null;
        }
        return result;
    }
}

