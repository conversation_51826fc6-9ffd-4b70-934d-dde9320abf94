package com.facishare.open.order.contacts.proxy.api.datasource;

import com.github.jedis.support.MergeJedisCmd;

/**
 * Created by chenzx on 2023/12/05
 */
public class RedisDataSource {
    private MergeJedisCmd jedisCmd;

    public MergeJedisCmd getRedisClient() {
        return jedisCmd;
    }

    public void setJedisCmd(MergeJedisCmd jedisCmd) {
        this.jedisCmd = jedisCmd;
    }

    public boolean isAllow(String key, Long limit, Long expireSeconds) {
        String luaScript =
                "local key = KEYS[1] " +
                        "local limit = tonumber(ARGV[1]) " +
                        "local expireSeconds = tonumber(ARGV[2]) " +
                        "local current = tonumber(redis.call('get', key) or '0') " +
                        "if current + 1 > limit then " +
                        "return 0 " + // 返回0表示没有增加计数，因为已经超过限制
                        "else " +
                        "redis.call('INCR', key) " +
                        "if current == 0 then " +
                        "redis.call('EXPIRE', key, expireSeconds) " + // 使用传入的过期时间
                        "end " +
                        "return 1 " + // 返回1表示成功增加计数
                        "end";

        // 调用eval，传入过期时间作为ARGV的第三个参数
        Object result = getRedisClient().eval(luaScript, 1, key, String.valueOf(limit), String.valueOf(expireSeconds));
        return Integer.parseInt(result.toString()) == 1;
    }
}