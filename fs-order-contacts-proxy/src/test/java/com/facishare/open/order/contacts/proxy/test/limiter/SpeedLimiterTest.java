package com.facishare.open.order.contacts.proxy.test.limiter;

import com.facishare.open.order.contacts.proxy.test.BaseTest;
import com.facishare.open.order.contacts.proxy.api.limiter.SpeedLimiter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SpeedLimiterTest extends BaseTest {
    @Autowired
    private SpeedLimiter speedLimiter;

    @Test
    public void test() {
        for (int i = 0; i < 10; i++) {
            Thread thread = new Thread(() ->{
                for (int j = 0; j < 5; j++) {
                    boolean allow = speedLimiter.isAllow("test1", 5L, 30L, 1);
                    System.out.println(allow);
                }
            }
                    );
            thread.start();
        }
        try {
            Thread.sleep(40000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
