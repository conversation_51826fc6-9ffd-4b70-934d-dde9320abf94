package com.facishare.open.order.contacts.proxy.service

import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class FsObjServiceProxyTest extends Specification {
    @Resource
    private FsObjServiceProxy fsObjServiceProxy

    def "listAllObjects"() {
        given:
        Integer eiCase = ei
        expect:
        def a = fsObjServiceProxy.listAllObjects(eiCase)
        print(a)
        where:
        ei  ||  result
        84883 ||  null
    }

    def "queryObjectData"() {
        given:
        Integer eiCase = ei
        String objectApiNameCase = objectApiName
        String objectIdCase = ObjectId
        expect:
        def a = fsObjServiceProxy.queryObjectData(eiCase, objectApiNameCase, objectIdCase)
        print(a)
        where:
        ei | objectApiName | ObjectId  ||  result
        90429 | "AccountObj" | "66ac4c6f7da2360008d2ab14"  ||  null
    }

    def "dealCrmTodo"() {
        expect:
        DealCrmTodoArg arg = new DealCrmTodoArg()
        arg.setTaskId("66a09ed6eed0f05dd3e74996")
        arg.setActionType("agree")
        arg.setOpinion("同意")
        def a = fsObjServiceProxy.dealCrmTodo(90429, "1000", arg)
        print(a)
    }
}
