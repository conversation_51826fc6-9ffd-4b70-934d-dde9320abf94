{"api_name": "WechatInterfaceLicenseObj", "display_name": "企微接口许可证", "is_udef": true, "store_table_name": "wechat_interface_license", "description": "用来存储企业微信接口许可下单相关信息", "define_type": "package", "icon_path": "A_201705_11_a0131af8e55549c68233e12bc53f5a60.png", "icon_index": 5, "config": {"edit": 1, "fields": {"add": 1}, "layout": {"add": 1, "assign": 1}, "record_type": {"add": 1, "assign": 1}, "cascade": {"add": 1}, "layout_rule": {"add": 1}, "rule": {"add": 1}, "button": {"add": 1}}, "is_active": true, "version": 1, "index_version": 1, "package": "CRM", "fields": {"extend_obj_data_id": {"is_required": false, "api_name": "extend_obj_data_id", "is_index": true, "status": "released", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "max_length": 64, "is_extend": false}, "name": {"description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "is_single": false, "max_length": 64, "is_index": true, "is_active": true, "label": "企微激活码", "api_name": "name", "help_text": "企微激活码", "status": "new"}, "account_status": {"is_index": true, "is_active": true, "is_unique": false, "label": "账号激活状态", "type": "select_one", "default_to_zero": false, "is_required": false, "api_name": "account_status", "options": [{"label": "未绑定", "value": 1}, {"label": "已绑定且有效", "value": 2}, {"label": "已过期", "value": 3}, {"label": "待转移", "value": 4}, {"label": "已合并", "value": 5}, {"label": "已分配给下游", "value": 6}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "账号激活状态", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "bind_wechat_userid": {"is_index": true, "is_active": true, "is_unique": false, "label": "激活码绑定的企业微信员工", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "bind_wechat_userid", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "企业微信员工id", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "order_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "订单ID", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "order_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "订单ID", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "to_active_code": {"is_index": true, "is_active": true, "is_unique": false, "label": "合并到激活码", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "to_active_code", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "该激活码合并到的新激活码信息", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "from_active_code": {"is_index": true, "is_active": true, "is_unique": false, "label": "被合并激活码", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "from_active_code", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "激活码激活userid时，若userid原来已经绑定了一个激活码，则会返回该字段", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "account_type": {"is_index": false, "is_active": true, "is_unique": false, "default_value": false, "label": "账号类型", "type": "select_one", "is_required": false, "api_name": "account_type", "options": [{"label": "基础账号", "value": 1}, {"label": "互通账号", "value": 2}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "1:基础账号，2:互通账号", "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "account_duration": {"label": "购买时长（天）", "default_value": "", "decimal_places": 0, "default_is_expression": false, "default_to_zero": false, "max_length": 14, "length": 14, "round_mode": 4, "field_num": 20, "type": "number", "define_type": "package", "api_name": "account_duration", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": false, "resource_bundle_key": null, "status": "new", "config": {"add": 0, "edit": 0, "remove": 0, "enable": 0, "display": 1, "attrs": {"label": 1, "api_name": 1, "help_text": 1, "default_value": 1, "is_unique": 1}}}, "active_time": {"is_index": true, "is_active": true, "is_unique": false, "is_required": false, "label": "账号激活时间", "time_zone": "GMT+8", "type": "date_time", "api_name": "active_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "pay_time": {"is_index": true, "is_active": true, "is_unique": false, "label": "下单购买时间", "time_zone": "GMT+8", "type": "date_time", "is_required": false, "api_name": "pay_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "expire_time": {"is_index": true, "is_active": true, "is_unique": false, "label": "账号失效时间", "time_zone": "GMT+8", "type": "date_time", "is_required": false, "api_name": "expire_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "active_expire_time": {"is_index": true, "is_active": true, "is_unique": false, "label": "激活截止时间", "time_zone": "GMT+8", "type": "date_time", "is_required": false, "api_name": "active_expire_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "remaining_time": {"label": "剩余时长", "api_name": "remaining_time", "type": "formula", "is_required": "false", "is_unique": "false", "description": "过期时间-今天 ", "help_text": "剩余时长 ", "status": "released", "expression": "IF(DATETIMETODATE($expire_time$)-DATETIMETODATE(NOW())<0,0,DATETIMETODATE($expire_time$)-DATETIMETODATE(NOW()))", "expression_type": "js", "return_type": "number", "define_type": "package", "default_to_zero": true, "decimal_places": 2, "resource_bundle_key": "CRM.Order.isNotify", "return_unit": "", "calculate_type": "number"}, "wechat_employee_id": {"label": "查找企业微信员工", "wheres": [], "where_type": "field", "target_api_name": "WechatEmployeeObj", "target_related_list_label": "企微接口许可证", "target_related_list_name": "wechat_employee_session_id", "action_on_target_delete": "set_null", "default_is_expression": false, "type": "object_reference", "define_type": "package", "api_name": "wechat_employee_id", "help_text": "", "is_required": false, "is_unique": false, "is_active": true, "is_index": true, "resource_bundle_key": null, "status": "new", "config": {"add": 0, "edit": 0, "remove": 0, "enable": 0, "display": 1, "attrs": {"label": 1, "api_name": 1, "help_text": 1, "target_api_name": 1, "target_related_list_label": 1, "target_related_list_name": 1, "wheres": 1, "default_value": 1, "is_unique": 1}}, "default_value": null}, "owner": {"label": "负责人", "is_single": true, "department_list": [], "api_name": "owner", "define_type": "package", "employee_list": [], "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "resource_bundle_key": null, "type": "employee", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"label": 0}}, "status": "new"}, "owner_department": {"label": "负责人主属部门", "default_value": "", "default_to_zero": false, "max_length": 100, "api_name": "owner_department", "define_type": "package", "department_list": [], "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": true, "is_unique": false, "page_acitve": true, "resource_bundle_key": null, "type": "text", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"label": 0}}, "status": "new"}, "record_type": {"label": "业务类型", "description": "", "api_name": "record_type", "define_type": "package", "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": false, "is_unique": false, "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "status": "released", "type": "record_type", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"label": 0}}, "resource_bundle_key": null}, "life_status": {"label": "生命状态", "default_value": "normal", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "maxItems": -1, "api_name": "life_status", "define_type": "package", "description": "", "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": false, "is_unique": false, "status": "new", "type": "select_one", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"default_value": 0, "options": 0, "label": 0, "help_text": 0, "is_required": 0}}, "resource_bundle_key": null}, "data_own_department": {"label": "归属部门", "is_single": true, "department_list": [], "api_name": "data_own_department", "define_type": "package", "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "page_acitve": true, "resource_bundle_key": null, "type": "department", "config": {"display": 1, "attrs": {"label": 0}}, "status": "new"}}}