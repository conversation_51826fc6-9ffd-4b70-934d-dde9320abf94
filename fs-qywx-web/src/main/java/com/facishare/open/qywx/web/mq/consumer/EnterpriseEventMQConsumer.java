package com.facishare.open.qywx.web.mq.consumer;

import com.facishare.open.qywx.web.mq.listener.EnterpriseEventListener;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Component
public class EnterpriseEventMQConsumer {
    @Resource
    private EnterpriseEventListener enterpriseEventListener;


    private AutoConfMQPushConsumer consumer;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-open-qywx-mq-cms",
                "QYWX_ENTERPRISE_REGISTER_SECTION",
                enterpriseEventListener);
        consumer.start();
        log.info("EnterpriseEventMQConsumer.init,consumer started");
    }
}
