package com.facishare.open.qywx.web.constant;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;

import java.io.InputStream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/16
 */
@AllArgsConstructor
public enum CreateObjectEnum {
    WechatConversionObj("WechatConversionObj",
            "/object_describe/WechatConversionObj/describe.json",
            "/object_describe/WechatConversionObj/layout.json",
            "/object_describe/WechatConversionObj/mobileList.json"),

    ;
    private String objectApiName;
    private String describePath;
    private String detailLayoutPath;
    private String mobileListLayoutPath;

    public String getDescribe(){
        return getResourceFile2String(this.describePath);
    }

    public String getDetailLayout(){
        return getResourceFile2String(this.detailLayoutPath);
    }

    public String getMobileListLayout(){
        return getResourceFile2String(this.mobileListLayoutPath);
    }

    public static String getResourceFile2String(String filePath) {
        try (InputStream inputStream = CreateObjectEnum.class.getResourceAsStream(filePath)) {
            return JSON.parseObject(inputStream, String.class);
        } catch (Exception e) {

        }
        return null;
    }
}
