package com.facishare.open.qywx.messagesend.enums;

/**
 * Created by fengyh on 2018/7/23.
 */
public enum QyWeixinMsgType {

    MSG_TEXT("text", "文本消息"),
    MSG_TEXT_CARD("textcard", "文本卡片消息"),
    MSG_NEWS_CARD("news", "图文消息（图片url）"),
    MINIPROGRAM_NOTICE("miniprogram_notice", "小程序通知消息"),
    ;

    private String type;               //消息类型
    private String description;        //消息类型描述

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    QyWeixinMsgType(String type, String description){
        this.type = type;
        this.description = description;
    }

}
