package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.Alias;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "qyweixin_id_to_openid")
@Alias("qyweixinIdToOpenidBo")
public class QyweixinIdToOpenidBo extends IdEntity implements Serializable {
    private String corpId;
    private String plaintextId;
    private String openid;
    private Integer type;
    private Timestamp createTime;
    private Timestamp updateTime;
}
