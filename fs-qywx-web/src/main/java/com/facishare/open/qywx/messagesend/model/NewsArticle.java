package com.facishare.open.qywx.messagesend.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/31
 *
 * 图文消息
 */
@Data
public class NewsArticle implements Serializable {


    private static final long serialVersionUID = -2916667697130780261L;
    //标题，不超过128个字节，超过会自动截断
    private String title;
    //描述，不超过512个字节，超过会自动截断  如：<div class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>
    private String description;
    //点击后跳转的链接。
    private String url;
    //图文消息的图片链接(外网可以范围)，支持JPG、PNG格式，较好的效果为大图 640x320，小图80x80。
    private String picurl;
    //按钮文字，仅在图文数为1条时才生效。 默认为“阅读全文”， 不超过4个文字，超过自动截断
    private String btntxt;

}
