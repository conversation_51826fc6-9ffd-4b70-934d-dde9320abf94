package com.facishare.open.qywx.web.core.enums;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/16
 */
@AllArgsConstructor
@Slf4j
public enum CreateObjectEnum {
    WechatInterfaceLicenseObj("WechatInterfaceLicenseObj",
            "/object_description_wechat_lincense/init_crm_WechatInterfaceLicenseObj_describe.json",
            "/object_description_wechat_lincense/layout.json",
            "/object_description_wechat_lincense/mobileList.json"),


    ;
    private String objectApiName;
    private String describePath;
    private String detailLayoutPath;
    private String mobileListLayoutPath;

    public String getDescribe(){
        return getResourceFile2String(this.describePath);
    }

    public String getDetailLayout(){
        return getResourceFile2String(this.detailLayoutPath);
    }

    public String getMobileListLayout(){
        return getResourceFile2String(this.mobileListLayoutPath);
    }

    public static String getResourceFile2String(String filePath) {
        try (InputStream inputStream = CreateObjectEnum.class.getResourceAsStream(filePath)) {
            return JSON.parseObject(inputStream, String.class);
        } catch (Exception e) {
           log.info("enum fail:{}",e.getMessage());
        }
       return null;
    }


}
