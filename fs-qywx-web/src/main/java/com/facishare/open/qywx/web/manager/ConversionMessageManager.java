package com.facishare.open.qywx.web.manager;//package com.facishare.open.qywx.web.manager;
//
//import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
//import com.facishare.open.qywx.eventhandler.mongo.dao.MessageSaveMongoDao;
//import com.mongodb.bulk.BulkWriteResult;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Component
//@Slf4j
//public class ConversionMessageManager {
//    @Resource
//    private MessageSaveMongoDao messageSaveMongoDao;
//
//    public void pushConversionMessage(CloudMessageProxyProto proxyProto) {
//        if(CollectionUtils.isNotEmpty(proxyProto.getAddConversionMessages())) {
//            BulkWriteResult integerResult = messageSaveMongoDao.batchReplace(proxyProto.getAddConversionMessages().get(0).getEi(), proxyProto.getAddConversionMessages());
//            log.info("ConversionMessageManager.PushConversionMessage,batch save message count:{},ea:{}",integerResult,proxyProto.getFsEa());
//        }
//
//        if(CollectionUtils.isNotEmpty(proxyProto.getUpdateConversionMessageIds())) {
//            BulkWriteResult integerResult = messageSaveMongoDao.batchUpdate(proxyProto.getUpdateConversionMessageIds().get(0).getEi(), proxyProto.getUpdateConversionMessageIds());
//            log.info("ConversionMessageManager.PushConversionMessage,batch update message count:{},ea:{}", integerResult, proxyProto.getFsEa());
//        }
//    }
//}
