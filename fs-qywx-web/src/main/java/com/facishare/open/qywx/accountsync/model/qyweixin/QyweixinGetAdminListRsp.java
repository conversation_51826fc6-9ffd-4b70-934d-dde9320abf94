package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinGetAdminListRsp implements Serializable {
    public QyweixinGetAdminListRsp(int errcode,String errmsg) {
        this.errcode = errcode;
        this.errmsg = errmsg;
    }
    private int errcode;
    private String errmsg;
    private List<AdminModel> admin; //应用的管理员列表（不包括外部管理员），成员授权模式下，不返回系统管理员

    @Data
    public static class AdminModel implements Serializable {
        private String userid;
        private String open_userid;
        private int auth_type; //该管理员对应用的权限：0=发消息权限，1=管理权限
    }

    public Boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
