package com.facishare.open.qywx.web.mq.sender;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.accountinner.model.OaconnectorEventDateChangeProto;
import com.facishare.open.qywx.accountinner.model.OutEventDateChangeProto;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.accountsync.model.CreateCrmEnterpriseEventProto;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto2;
import com.facishare.open.qywx.web.arg.ConversionChangeArg;
import com.facishare.open.qywx.web.arg.MQArg;
import com.facishare.open.qywx.web.arg.MessageGeneratingArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MQ发送专用工具
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class MQSender {
    @Resource(name = "enterpriseWechatEventMQSender")
    private AutoConfRocketMQProducer enterpriseWechatEventMQSender;

    @Resource(name = "outEventDataChangeMQSender")
    private AutoConfRocketMQProducer outEventDataChangeMQSender;


    @Resource(name = "oaconnectorEventDataChangeMQSender")
    private AutoConfRocketMQProducer oaconnectorEventDataChangeMQSender;

    @Resource(name = "secretMessageSender")
    private AutoConfRocketMQProducer secretMessageSender;

    @Resource(name = "conversionSettingMessageSender")
    private AutoConfRocketMQProducer conversionSettingMessageSender;

    @Resource(name = "conversionChangeMessageSender")
    private AutoConfRocketMQProducer conversionChangeMessageSender;

    @Resource
    private EIEAConverter eieaConverter;

    /**
     * 发送企业微信解密后的事件MQ
     * @param tag 事件类型tag
     * @param plainMsg 明文企微事件消息
     */
    public void sendEnterpriseWeChatMQ(String tag,String plainMsg,String appId) {
        log.info("MQSender.sendEnterpriseWeChatMQ,plainMsg={}", plainMsg);

        EnterpriseWeChatEventProto2 eventProto2 = new EnterpriseWeChatEventProto2();
        eventProto2.setPlainMsg(plainMsg);
        eventProto2.setAppId(appId);
        Message msg = new Message();
        msg.setTags(tag);
        msg.setBody(eventProto2.toProto());
        SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
        log.info("MQSender.sendEnterpriseWeChatMQ,sendResult={},plainMsg={}", sendResult, plainMsg);
    }


    public void sendEnterpriseCreateMQ(String tag, CreateCrmEnterpriseEventProto eventProto) {
        Message msg = new Message();
        msg.setTags(tag);
        msg.setBody(eventProto.toProto());
        SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
        log.info("MQSender.sendEnterpriseCreateMQ,sendResult={},eventProto={}", sendResult, eventProto);
    }

    public void sendCloudProxyMQ(Integer ei, CloudMessageProxyProto proto) {
        Message msg = new Message();
        msg.setTags("cloud_proxy_event_tag");
        msg.setBody(proto.toProto());

        //把纷享云的MQ投递到所有的专属云
        TraceContext context = TraceContext.get();
        context.setEi(String.valueOf(ei));

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("MQSender.sendCloudProxyMQ,sendResult={}",sendResult);
    }

    public SendResult sendOutEventDataChangeMQ(String tag, OutEventDateChangeProto proto, String ei) {
        Message msg = new Message();
        msg.setTopic(outEventDataChangeMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(proto.toProto());

        TraceContext context = TraceContext.get();
        context.setEi(ei);  // 可以区分是否跨云投递
        context.setTraceId(TraceUtils.getTraceId());  // 日记记录，分布式跟踪需要

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("MQSender.sendOutEventDataChangeMQ.ei={},tag={},proto={},sendResult={}", ei, tag, proto, sendResult);
        return sendResult;
    }

    public SendResult sendOaconnectorEventDataChangeMQ(String tag, OaconnectorEventDateChangeProto proto, String ei) {
        Message msg = new Message();
        msg.setTopic(oaconnectorEventDataChangeMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(proto.toProto());

        TraceContext context = TraceContext.get();
        context.setEi(ei);  // 可以区分是否跨云投递
        context.setTraceId(TraceUtils.getTraceId());  // 日记记录，分布式跟踪需要

        SendResult sendResult = oaconnectorEventDataChangeMQSender.send(msg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("MQSender.sendOaconnectorEventDataChangeMQ.ei={},tag={},sendResult={}", ei, tag, sendResult);
        return sendResult;
    }

    public void sendToSecret(MQArg mqArg) {
        Message message = new Message();
        String body = JSONObject.toJSONString(mqArg);
        message.setBody(body.getBytes());
        if(ConfigCenter.TEM_CLOUD_EA.contains(mqArg.getEaList().get(0))) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.secretMessageSender.name());
            cloudMessageProxyProto.setFsEa(mqArg.getEaList().get(0));
            cloudMessageProxyProto.setMessage(message);
            //跨云
            this.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(mqArg.getEaList().get(0)), cloudMessageProxyProto);
        } else {
            SendResult sendResult = secretMessageSender.send(message);
            log.info("secretMessageSender sendToSecretProxy success. mqArg:{}, sendResult:{}", mqArg, sendResult);
        }
    }

    public void sendToConversionSetting(MessageGeneratingArg messageGeneratingArg, String tag) {
        Message message = new Message();
        String body = JSONObject.toJSONString(messageGeneratingArg);
        message.setBody(body.getBytes());
        message.setTags("tag_qywx_conversion_" + tag);
        if(ConfigCenter.TEM_CLOUD_EA.contains(messageGeneratingArg.getEa())) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.conversionSettingMessageSender.name());
            cloudMessageProxyProto.setFsEa(messageGeneratingArg.getEa());
            cloudMessageProxyProto.setMessage(message);
            //跨云
            this.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(messageGeneratingArg.getEa()), cloudMessageProxyProto);
        } else {
            SendResult sendResult = conversionSettingMessageSender.send(message);
            log.info("sendToConversionSetting sendToSecretProxy success. messageGeneratingArg:{}, sendResult:{}", messageGeneratingArg, sendResult);
        }
    }

    public void sendToConversionChange(ConversionChangeArg conversionChangeArg) {
        Message message = new Message();
        String body = JSONObject.toJSONString(conversionChangeArg);
        message.setBody(body.getBytes());
        if(ConfigCenter.TEM_CLOUD_EA.contains(conversionChangeArg.getEa())) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.conversionChangeMessageSender.name());
            cloudMessageProxyProto.setFsEa(conversionChangeArg.getEa());
            cloudMessageProxyProto.setMessage(message);
            //跨云
            this.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(conversionChangeArg.getEa()), cloudMessageProxyProto);
        } else {
            SendResult sendResult = conversionChangeMessageSender.send(message);
            log.info("conversionChangeMessageSender sendToSecretProxy success. mqArg:{}, sendResult:{}", conversionChangeArg, sendResult);
        }
    }
}
