package com.facishare.open.qywx.web.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by fengyh on 2018/4/23.
 */
@Data
public class QyweixinGetSuittokenRsp implements Serializable {
    private int errcode;
    private String errmsg;
    private String suite_access_token;
    private int expires_in;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
