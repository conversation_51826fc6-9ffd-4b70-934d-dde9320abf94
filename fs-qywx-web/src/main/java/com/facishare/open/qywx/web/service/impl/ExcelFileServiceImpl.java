package com.facishare.open.qywx.web.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.core.enums.TemplateTypeEnum;
import com.facishare.open.qywx.accountsync.excel.*;
import com.facishare.open.qywx.accountsync.excel.vo.DepartmentMappingVo;
import com.facishare.open.qywx.accountsync.excel.vo.EmployeeMappingVo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ExcelFileService;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.excel.DepartmentDataMappingListener;
import com.facishare.open.qywx.web.excel.EmployeeDataMappingListener;
import com.facishare.open.outer.oa.connector.i18n.qywx.CustomCellWriteHandler;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("excelFileService")
public class ExcelFileServiceImpl implements ExcelFileService {
    @Autowired
    private FileManager fileManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private ToolsService toolsService;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    public <R> Result<BuildExcelFile.Result> buildExcelFile(BuildExcelFile.Arg arg) {
        String ea = arg.getEa();
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        if (arg.getSheetDataList().isEmpty()) {
            return Result.newInstance(ErrorRefer.DATA_LIST_CANNOT_EMPTY);
        }
        //写excel
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy styleStrategy = arg.getStyle();
        if (styleStrategy == null) {
            styleStrategy = ExcelUtils.getDefaultStyle();
        }
        ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        String lang = TraceUtil.getLocale();
        CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager,tenantId,lang);
        for (String sheetName : arg.getSheetDataList().keySet()) {
            List<R> dataList = arg.getSheetDataList().get(sheetName);
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName)
                    .head(dataList.get(0).getClass())
                    .registerWriteHandler(customCellWriteHandler)
                    .build();
            excelWriter.write(dataList, writeSheet);
        }
        excelWriter.finish();
//        byte[] data = outputStream.toByteArray();
//        try {
//            FileUtils.writeByteArrayToFile(new File("d://test/test.xlsx"),data);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, -10000, outputStream.toByteArray());
        BuildExcelFile.Result result = new BuildExcelFile.Result();
        String fileName = StringUtils.appendIfMissing(arg.getFileName(), ".xlsx");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }

    @Override
    public Result<BuildExcelFile.Result> buildExcelTemplate(String ea, TemplateTypeEnum templateType) {
        String lang = TraceUtil.getLocale();
        log.info("buildExcelTemplate,lang={}",lang);
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        switch (templateType) {
            case DEPARTMENT_EMPLOYEE_BIND:
                Map<String,List> sheetMapList = new HashMap<>();
                sheetMapList.put(i18NStringManager.get(I18NStringEnum.s103,lang,tenantId),
                        Lists.newArrayList(DepartmentMappingVo.getTempData(i18NStringManager,lang,tenantId)));
                sheetMapList.put(i18NStringManager.get(I18NStringEnum.s104,lang,tenantId),
                        Lists.newArrayList(EmployeeMappingVo.getTempData(i18NStringManager,lang,tenantId)));

                BuildExcelFile.Arg arg = new BuildExcelFile.Arg();
                arg.setEa(ea);
                arg.setTemplateType(templateType);
                arg.setFileName(i18NStringManager.get(I18NStringEnum.s105,lang,tenantId));
                arg.setSheetDataList(sheetMapList);
                return buildExcelFile(arg);
        }
        return new Result<>();
    }

    @Override
    public Result<ImportExcelFile.Result> importExcelFile(ImportExcelFile.MappingDataArg arg, String lang) throws IOException {
        log.info("importExcelFile,lang={}",lang);
        switch (arg.getTemplateType()) {
            case DEPARTMENT_EMPLOYEE_BIND:
                byte[] bytes = fileManager.downTnFile(arg.getFsEa(), arg.getFsUserId(), arg.getNpath());
                if(bytes==null) {
                    return Result.newInstance(ErrorRefer.NPATH_FILE_DOWNLOAD_FAILED);
                }
//                String mainAppId = qyweixinGatewayInnerService.getMainAppId(arg.getOutEa()).getData();
//                arg.setAppId(mainAppId);
                OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, arg.getFsEa(), arg.getOutEa(), arg.getAppId());
                log.info("importExcelFile,enterpriseBindEntity={}.", enterpriseBindEntity);
                if (ObjectUtils.isEmpty(enterpriseBindEntity)) {
                    return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
                }
                Result<ImportExcelFile.Result> result = importDepartmentDataMapping(arg, enterpriseBindEntity,bytes,lang);
                Result<ImportExcelFile.Result> result2 = importEmployeeDataMapping(arg, enterpriseBindEntity,bytes,lang);

                Result<ImportExcelFile.Result> importResult = new Result<>();
                importResult.setData(new ImportExcelFile.Result());
                if(!result.isSuccess()) {
                    importResult.setErrorCode(result.getErrorCode());
                    importResult.setErrorMsg(result.getErrorMsg());
                }
                if(!result2.isSuccess()) {
                    importResult.setErrorCode(result2.getErrorCode());
                    importResult.setErrorMsg(importResult.getErrorMsg() + "\n" + result2.getErrorMsg());
                }
                importResult.getData().setPrintMsg(result.getData().getPrintMsg() + "\n" + result2.getData().getPrintMsg());
                importResult.getData().setInvokedNum(result.getData().getInvokedNum() + result2.getData().getInvokedNum());
                importResult.getData().setInsertNum(result.getData().getInsertNum() + result2.getData().getInsertNum());
                sendImportResult(arg.getFsEa(), arg.getFsUserId(), importResult,lang);
                return importResult;
        }
        return new Result<>();
    }

    private void sendImportResult(String fsEa, Integer userId, Result<ImportExcelFile.Result> importResult, String lang) {
        log.info("sendImportResult,importResult={}",importResult);
        String msg = importResult.getErrorMsg();
        if (importResult.isSuccess()) {
            msg = importResult.getData().getPrintMsg();
        }

        String fsEi = eieaConverter.enterpriseAccountToId(fsEa)+"";
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(fsEi);
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s189,lang,null) + LocalDateTime.now().toString());
        notificationService.sendQYWXNotice(sendTextNoticeArg);
    }

    private <T> Result<ImportExcelFile.Result> importDepartmentDataMapping(ImportExcelFile.MappingDataArg arg, OuterOaEnterpriseBindEntity enterpriseBindEntity, byte[] bytes, String lang) throws IOException {
        DepartmentDataMappingListener listener = new DepartmentDataMappingListener(enterpriseBindEntity,
                arg.getFsUserId(),
                qyweixinAccountBindService,
                qyweixinAccountSyncService,
                qyweixinGatewayInnerService,
                toolsService,
                i18NStringManager,
                lang,
                outerOaDepartmentBindManager);

        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        //读取excel
        ReadExcel.Arg<DepartmentMappingVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(DepartmentMappingVo.class);
        readExcelArg.setInputStream(bis);
        readExcelArg.setSheetName(i18NStringManager.get(I18NStringEnum.s103,lang,null));
        fileManager.readExcelBySheetName(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importDepartmentDataMapping arg:{},result:{}", arg, result);
        return new Result<>(result);
    }

    private <T> Result<ImportExcelFile.Result> importEmployeeDataMapping(ImportExcelFile.MappingDataArg arg, OuterOaEnterpriseBindEntity enterpriseBindEntity, byte[] bytes, String lang) throws IOException {
        EmployeeDataMappingListener listener = new EmployeeDataMappingListener(enterpriseBindEntity,
                arg.getFsUserId(),
                qyweixinAccountBindService,
                toolsService,
                fsEmployeeServiceProxy,
                qyweixinGatewayInnerService,
                eieaConverter,
                i18NStringManager,
                lang,
                outerOaEmployeeBindManager);

        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        //读取excel
        ReadExcel.Arg<EmployeeMappingVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(EmployeeMappingVo.class);
        readExcelArg.setInputStream(bis);
        readExcelArg.setSheetName(i18NStringManager.get(I18NStringEnum.s104,lang,null));
        log.info("importEmployeeDataMapping,readExcelArg={}", readExcelArg);
        fileManager.readExcelBySheetName(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importEmployeeDataMapping,arg:{},result:{}", arg, result);
        return new Result<>(result);
    }
}
