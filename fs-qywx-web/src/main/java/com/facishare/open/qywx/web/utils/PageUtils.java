package com.facishare.open.qywx.web.utils;

import com.facishare.open.qywx.save.result.Pager;
import com.github.pagehelper.PageInfo;
import lombok.experimental.UtilityClass;

import java.util.List;

/**
 * Create by max on 2019/07/11
 **/
@UtilityClass
public class PageUtils {

    /**
     * 将PageHelper分页信息转为自身需要的信息
     *
     * @param t        数据
     * @param pageInfo PageHelper分页信息
     * @param <T>      类型
     * @return Pager
     */
    public <T> Pager transformData(List<T> t, PageInfo pageInfo) {
        Pager<T> pager = new Pager<>();

        pager.setPageNum(pageInfo.getPageNum());
        pager.setPageSize(pageInfo.getPageSize());
        pager.setTotal(pageInfo.getTotal());
        pager.setPages(pageInfo.getPages());
        pager.setFirstPage(pageInfo.isIsFirstPage());
        pager.setLastPage(pageInfo.isIsLastPage());
        pager.setHasPreviousPage(pageInfo.isHasPreviousPage());
        pager.setHasNextPage(pageInfo.isHasNextPage());

        pager.setData(t);

        return pager;
    }

    public <T> Pager transformData1(List<T> t, Integer num, Integer pageSize, Boolean isHasNextPage) {
        Pager<T> pager = new Pager<>();

        pager.setPageNum(num);
        pager.setPageSize(pageSize);
        pager.setHasNextPage(isHasNextPage);
        pager.setData(t);

        return pager;
    }
}