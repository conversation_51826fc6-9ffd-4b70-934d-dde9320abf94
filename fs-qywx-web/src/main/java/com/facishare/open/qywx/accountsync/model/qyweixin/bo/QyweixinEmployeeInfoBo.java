package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 企业微信通讯录数据（用于企业微信搜索选择）
 * Created by <PERSON><PERSON><PERSON> on 2018/09/27
 */
@Data
@Table(name = "qyweixin_employee_account")
public class QyweixinEmployeeInfoBo implements Serializable {

    private String corpId;
    private String userId;
    private String department_Ids;
    private String name;
    private String mobile;
    private String email;

    public QyweixinEmployeeInfoBo(String corpId, String userId, String department_Ids, String name, String mobile, String email) {
        this.corpId = corpId;
        this.userId = userId;
        this.department_Ids = department_Ids;
        this.name = name;
        this.mobile = mobile;
        this.email = email;
    }
}
