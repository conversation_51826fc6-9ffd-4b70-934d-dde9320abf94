package com.facishare.open.qywx.web.enums;

import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2020/10/14 16:23
 * @Version 1.0
 */


@Getter
public enum CrmMessageEnum {

    CRMTYPE("CRM","CRM消息", I18NStringEnum.s254),
    TOBEREPLENISHSTOCK("tobeReplenishStock","库存补货通知", I18NStringEnum.s255),
    EXPIRINGINVENTORY("expiringInventory","库存临期通知", I18NStringEnum.s256),
    APPROVAL("approval","审批消息", I18NStringEnum.s257),
    WORKFLOW("workflow","工作流消息", I18NStringEnum.s258),
    IMPORTASSISTANT("importAssistant","导入助手", I18NStringEnum.s259),
    PRM("PRM","PRM消息", I18NStringEnum.s260),
    APPROVALFLOW("approvalFlow","发起的审批流程", I18NStringEnum.s261),
    CRMNOTIFY("crmNotify","CRM通知", I18NStringEnum.s262),
            ;
    private String appId;
    private String messageType;
    private I18NStringEnum i18NStringEnum;


    CrmMessageEnum(String appId, String messageType, I18NStringEnum i18NStringEnum) {
        this.appId = appId;
        this.messageType = messageType;
        this.i18NStringEnum = i18NStringEnum;
    }

    public static CrmMessageEnum getAppId(String appId){
        for (CrmMessageEnum e : CrmMessageEnum.values()){
            if(e.appId.equals(appId)){
                return e;
            }
        }
        //都没有的话，给默认为crm消息
        return CRMTYPE;
    }



}
