package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * 外部联系人事件模型
 */

@XStreamAlias("xml")
@Data
public class ExternalChatEventXml implements Serializable {
    @XStreamAlias("SuiteId")
    public String SuiteId;

    @XStreamAlias("InfoType")
    public String InfoType;

    @XStreamAlias("TimeStamp")
    public String TimeStamp;

    @XStreamAlias("AuthCorpId")
    public String AuthCorpId;//企业微信CorpID

    @XStreamAlias("ToUserName")
    public String ToUserName;//企业微信CorpID

    @XStreamAlias("ChangeType")
    public String ChangeType;

    @XStreamAlias("ChatId")
    public String ChatId;

    @XStreamAlias("UpdateDetail")
    public String UpdateDetail;

    @XStreamAlias("JoinScene")
    public String JoinScene;

    @XStreamAlias("QuitScene")
    public String QuitScene;

    @XStreamAlias("MemChangeCnt")
    public String MemChangeCnt;
}
