package com.facishare.open.qywx.web.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class ErasePasswordUtils {
    private static final List<String> keyWorlds = Lists.newArrayList("password", "token", "secret");
    private static final Integer replaceSize = 20;
    private static final String replacement = "**********";

    public static String erase(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                return msg;
            }
            for (String keyWorld : keyWorlds) {
                //需要忽略大小写
                if (StringUtils.containsIgnoreCase(msg, keyWorld)) {
                    for (int i = 0; i < 5; i++) {
                        int keywordIndex = StringUtils.indexOfIgnoreCase(msg, keyWorld);
                        if (keywordIndex != -1) {
                            String substring;
                            if (keywordIndex + keyWorld.length() + replaceSize <= msg.length()) {
                                substring = msg.substring(keywordIndex + keyWorld.length(), keywordIndex + keyWorld.length() + replaceSize);
                            } else {
                                substring = msg.substring(keywordIndex + msg.length(), msg.length());
                            }
                            if (substring.contains(",")) {
                                substring = substring.substring(0, substring.indexOf(','));
                            }
                            if (substring != null) {
                                msg = StringUtils.replaceIgnoreCase(msg, keyWorld + substring, replacement);
                            }
                        } else {
                            break;
                        }
                    }
                }
            }
        }catch (Exception e){

        }
        return msg;
    }

    public static void main(String[] args) {
        String str = "channel=ERP_K3CLOUD, passwordenterpriseName=广州誉维生物科技仪器有限公司, connectParams=ConnectInfoResult.ConnectParams(u8=null, sap=null, k3Cloud=K3CloudConnectParam(baseUrl=https://kingdeecloud.ewell.com.cn/k3cloud, dbId=6115242cc38d9a, dbName=誉维生物, authType=1, userName=金蝶对接纷享账号, password=GJZ9k#mCspwJJfh3, appId=null, lcid=2052, pushDataApiNames=[], useFsHttpClient=true, config=K3CloudApiConfig(useAppToken=false, secret=test, enableDebug=false, removeZeroWidthChar=false)), standard=null, u8Eai=null, dbProxy=null, zhiHu=null";
        String llll = ErasePasswordUtils.erase(str);
        System.out.println("");
    }
}
