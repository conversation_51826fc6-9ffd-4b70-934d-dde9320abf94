package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
@Data
public class QyweixinDepartmentInfo implements Serializable {

    private static final long serialVersionUID = 9043910436076007302L;

    private String appId;

    //创建的部门id
    private String id;

    //部门名称
    private String name;

    //父亲部门id   id==0的是跟部门
    private String parentId;

    //在父部门中的次序值。order值大的排序靠前。值范围是[0, 2^32)
    private String order;

    /**
     * @see QyweixinEmployeeInfo#DUMMY_NAME_PREFIX
     */
    public String getName() {
        if (id != null && id.equals(name)) {
            return "D-" + QyweixinEmployeeInfo.DUMMY_NAME_PREFIX + name;
        }
        return name;
    }
}
