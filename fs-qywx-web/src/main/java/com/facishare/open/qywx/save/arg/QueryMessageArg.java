package com.facishare.open.qywx.save.arg;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:29
 * @Version 1.0
 */
@Data
public class QueryMessageArg implements Serializable {
    private String fsEa;
    private String outEa;
    private Integer empId;
    private Long lastMessageTime;//保留字段 前端传值则默认前端值，空则默认当前时间
    private String senderIds;//发送方ID，单数ID
    private String receiveIds;//接收方ID，传对应的外部联系人id，单数ID
    private Long preMessageTime;//最早时间
    private Integer pageNum=1;//当前页
    private Integer pageSize=20;//每页的数量
    private Integer labelValue;//对应前端约定的数据
    private Integer limit;
    private String roomId;
}
