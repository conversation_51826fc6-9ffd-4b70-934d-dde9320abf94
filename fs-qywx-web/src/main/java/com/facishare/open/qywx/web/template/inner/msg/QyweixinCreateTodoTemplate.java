package com.facishare.open.qywx.web.template.inner.msg;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FunctionMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.function.FunctionMsgBase;
import com.facishare.open.qywx.web.arg.CreateTodoPushArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.limiter.CrmMessagePullLimiter;
import com.facishare.open.qywx.web.service.ExternalTodoMsgService;
import com.fxiaoke.message.extrnal.platform.model.ExternalEventTag;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 飞书创建待办模板
 * <AUTHOR>
 * @date 20241225
 */

@Slf4j
@Component
public class QyweixinCreateTodoTemplate extends SendMsgHandlerTemplate {
    @Resource
    private ExternalTodoMsgService externalTodoMsgService;
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;

    @Override
    protected void filterMsg(MethodContext context) {
        log.info("QyweixinCreateTodoTemplate.filterMsg,context={}",context);
        CreateTodoPushArg createTodoPushArg = context.getData();

        if (ObjectUtils.isEmpty(createTodoPushArg)) {
            context.setResult(TemplateResult.newError(null));
            return;
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getOuterOaEnterpriseBindEntity();
        if (!enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.normal)) {
            log.info("QyweixinCreateTodoTemplate.filterMsg.is not normal,enterpriseBindEntity={}", enterpriseBindEntity);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
        if (!qywxConnectorVo.isAlertConfig()) {
            log.info("QyweixinCreateTodoTemplate.filterMsg.is not alert config,qywxConnectorVo={}", qywxConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        if (!qywxConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_TODO)) {
            log.info("QyweixinCreateTodoTemplate.filterMsg.if not todo type,qywxConnectorVo={}", qywxConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }

        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();

        String fsEa = createTodoArg.getEa();
        Integer fsEi = createTodoArg.getEi();

        FunctionMsgBase functionMsgBase = new FunctionMsgBase();
        functionMsgBase.setChannel(enterpriseBindEntity.getChannel().name());
        functionMsgBase.setFsEa(fsEa);
        functionMsgBase.setOutEa(outEa);
        functionMsgBase.setAppId(appId);
        functionMsgBase.setType(FunctionMsgTypeEnum.crmExternalMsgPush.getType());
        functionMsgBase.setEventType(ExternalEventTag.CreateTodo);
        functionMsgBase.setData(JSON.toJSONString(createTodoArg));

        // todo:函数过滤，先放在配置中心吧
        Map<String, String> filterCalendarEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });

        //根据函数判断是否继续
        if (filterCalendarEaMap.containsKey(fsEa) && StringUtils.isNotEmpty(filterCalendarEaMap.get(fsEa))) {
            Boolean isPull = crmMessagePullLimiter.messageIsPull(fsEi, filterCalendarEaMap.get(fsEa), functionMsgBase);
            if (!isPull) {
                context.setResult(TemplateResult.newError(null));
                return;
            }
        }

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void buildMsg(MethodContext context) {
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("QyweixinCreateTodoTemplate.sendMsg,context={}",context);
        CreateTodoPushArg createTodoPushArg = context.getData();
        externalTodoMsgService.createTodo(createTodoPushArg);
        context.setResult(TemplateResult.newSuccess());
    }
}
