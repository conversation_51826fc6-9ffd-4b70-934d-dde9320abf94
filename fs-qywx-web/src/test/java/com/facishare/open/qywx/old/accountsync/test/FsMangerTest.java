package com.facishare.open.qywx.old.accountsync.test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.qywx.web.manager.FsManager;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class FsMangerTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    private FsManager fsManager;

    @Test
    public void queryFsObjectTest() {
        String s = fsManager.queryFsObject(84883, "owner", "1021", "AccountObj");
        JSONArray read = (JSONArray) JSONPath.read(s, "$.data.dataList");
        System.out.println(read);
    }

    @Test
    public void getEnterpriseRunStatusTest() {
        GetEnterpriseRunStatusResult s = fsManager.getEnterpriseRunStatus("76517");
        System.out.println(s);
    }

    @Test
    public void getEnterpriseDataTest() {
        GetEnterpriseDataResult s = fsManager.getEnterpriseData("765171");
        System.out.println(s);
    }

    @Test
    public void getDepartment() {
        Department department = fsManager.getDepartment("88521",9999);
        System.out.println(department);
    }
}
