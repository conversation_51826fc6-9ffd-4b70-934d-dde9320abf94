package com.facishare.open.qywx.old.accountsync.test;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.*;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.open.qywx.accountsync.model.FileUploadModel;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.FileUploadService;
import com.google.common.collect.Lists;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class FileUploadServiceTest {
    @Resource
    private FileUploadService fileUploadService;
    @Autowired
    private NFileStorageService nFileStorageService;

//    @Test
//    public void upload2qywx() {
//        Result<Void> result = fileUploadService.upload("74860",
//                Lists.newArrayList("TN_ef98542790524018b012d1a867911e78"), "video");
//        System.out.println(result);
//        while (true) {
//
//        }
//    }

    @Test
    public void query() {
        Result<FileUploadModel> result = fileUploadService.query("74860","TN_35236cac7b5146dbb69535bba88a58c1", null);
        System.out.println(result);
    }

    @Test
    public void upload() throws IOException {
        File file = new File("D:\\test\\对象字段分析结果.json");
        byte[] bytes = FileUtils.readFileToByteArray(file);
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setEa("74860");
        arg.setExtensionName("json");
        arg.setOriginName("对象字段分析结果.json");
        arg.setData(bytes);
        NTempFileUpload.Result result = nFileStorageService.nTempFileUpload(arg, "74860");
        System.out.println(result);
    }

    @Test
    public void getFileTotalSize() {
        NGetFileTotalSize.Arg arg = new NGetFileTotalSize.Arg();
        arg.setEa("81961");
        arg.setPathList(Lists.newArrayList("TN_603fda4ca6cd475ea9d3e8753be121d2"));
        NGetFileTotalSize.Result result = nFileStorageService.getFileTotalSize(arg, "81961");
        System.out.println(result);
    }

    @Test
    public void nGetFileMetaData() {
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
        arg.setEa("81961");
        arg.setFileName("TN_603fda4ca6cd475ea9d3e8753be121d2");
        NGetFileMetaData.Result result = nFileStorageService.nGetFileMetaData(arg, "81961");
        System.out.println(result);
    }

    @Test
    public void nDownloadFile() {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa("82777");
        arg.setnPath("N_202209_21_83b11b96fe3243fe9ab2e9e224dd4359.docx");
        arg.setDownloadSecurityGroup("XiaoKeNetDisk");
        NDownloadFile.Result result = nFileStorageService.nDownloadFile(arg, "82777");
        System.out.println(result);
    }

    @Test
    public void getFileNames() {
        NGetFileName.Arg arg = new NGetFileName.Arg();
        arg.setEa("82777");
        arg.setPathList(Lists.newArrayList("N_202209_21_83b11b96fe3243fe9ab2e9e224dd4359.docx"));
        NGetFileName.Result result = nFileStorageService.getFileNames(arg, "82777");
        System.out.println(result);
    }
}
