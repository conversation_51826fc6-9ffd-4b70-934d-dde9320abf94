package com.facishare.open.aliyun.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.market20151101.models.DescribeOrderResponseBody;
import com.facishare.open.aliyun.market.config.ConfigCenter;
import com.facishare.open.aliyun.market.entity.EnterpriseBindEntity;
import com.facishare.open.aliyun.market.entity.OrderInfoEntity;
import com.facishare.open.aliyun.market.enums.*;
import com.facishare.open.aliyun.market.manager.EnterpriseBindManager;
import com.facishare.open.aliyun.market.manager.OrderInfoManager;
import com.facishare.open.aliyun.market.model.OrderModel;
import com.facishare.open.aliyun.market.model.VersionModel;
import com.facishare.open.aliyun.market.model.result.CreateOrderResult;
import com.facishare.open.aliyun.market.result.Result;
import com.facishare.open.aliyun.market.result.ResultCodeEnum;
import com.facishare.open.aliyun.market.service.AliyunHttpService;
import com.facishare.open.aliyun.market.service.OrderEventService;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.enums.CustomerSourceEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.EnterpriseUtils;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Service("orderEventServiceImpl")
public class OrderEventServiceImpl implements OrderEventService {
    @Resource
    private OrderInfoManager orderInfoManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;
    @Resource
    private AliyunHttpService aliyunHttpService;

    @Override
    public Result<CreateOrderResult> upgradeInstance(OrderModel orderModel) {
        CreateOrderResult createOrderResult = new CreateOrderResult();
        //根据订单id查询订单详情
        Result<DescribeOrderResponseBody> orderInfoResult = null;
        try {
            orderInfoResult = aliyunHttpService.getOrderInfo(orderModel.getOrderId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,orderInfoResult={}.",orderInfoResult);
        if(!orderInfoResult.isSuccess() || ObjectUtils.isEmpty(orderInfoResult.getData())) {
            LogUtils.info("OrderEventServiceImpl.upgradeInstance,get orderInfo fail,orderModel={}.",orderModel);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }
        DescribeOrderResponseBody orderInfo = orderInfoResult.getData();
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,orderInfo={}.",orderInfo);
        if(!orderInfo.getOrderStatus().equals("NORMAL")) {
            LogUtils.info("OrderEventServiceImpl.upgradeInstance,order is not normal,orderInfo={}.",orderInfo);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }

        if(!orderInfo.getPayStatus().equals("PAID")) {
            LogUtils.info("OrderEventServiceImpl.upgradeInstance,order is not PAID,orderInfo={}.",orderInfo);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }

        //获取订单规格
        String productCode = orderInfo.getProductCode();
        Map<String, Object> packageVersionObject = (Map<String, Object>) orderInfo.getComponents().get("package_version");
        String skuId = packageVersionObject.get("value").toString();
        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(productCode,skuId);
        AliyunProductEditionEnum crmEditionEnum = AliyunProductEditionEnum.valueOf(versionModel.getVersion());
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,versionModel={},crmEditionEnum={}.", versionModel, crmEditionEnum);
        if(crmEditionEnum==null) {
            LogUtils.info("OrderEventServiceImpl.upgradeInstance,product plan id not support={}.",skuId);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }

        //企业outEa
        String outEa = orderInfo.getAliUid().toString() + "-" + (StringUtils.isNotEmpty(orderModel.getInstanceId()) ? orderModel.getInstanceId() : orderModel.getOrderBizId());
        //实施服务包需要先创建企业才可以下单
        if(productCode.equals("cmfw00059470")) {
            List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindListByUid(orderInfo.getAliUid().toString());
            if(CollectionUtils.isEmpty(enterpriseBindList)) {
                LogUtils.info("OrderEventServiceImpl.upgradeInstance,outEa not bind,orderInfo={}.", orderInfo);
                return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
            }
            outEa = enterpriseBindList.get(0).getOutEa();
        }
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,outEa={}.", outEa);

        Integer userCount = null;
        //对于CRM和实施服务包外的产品，是没有购买人数的
        if(!ConfigCenter.CRM_PRODUCT_NUM.contains(skuId)) {
            //没有购买人数的产品，也没有试用
            userCount = ********;
        } else {
            //购买数量
            Map<String, Object> accountNumObject = (Map<String, Object>) orderInfo.getComponents().get("account_num");
            //试用版seats的值为0，我们默认把它改成10000，对客户来说，人数不会超过这个数，也就是人数不受限制
            userCount = accountNumObject.get("value").toString().equals("0") ? 10000 : Integer.parseInt(accountNumObject.get("value").toString());
        }
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,userCount={}", userCount);

        //获取订单的类型
        //获取购买的天数，阿里云购买方式为周期性购买
        Long orderStartTime = orderInfo.getPaidOn();
        Long orderEndTime = orderInfo.getPaidOn();
        Integer orderDays = null;
        PricePlanTypeEnum pricePlanType = null;
        BuyTypeEnum buyType = null;
        switch (orderInfo.getOrderType()) {
            case "NEW":
                buyType = BuyTypeEnum.buy;
                if(orderInfo.getPeriodType().equals("YEAR")) {
                    orderDays = GlobalValue.DAYS_OF_ONE_YEAR + 1;
                    orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    pricePlanType = PricePlanTypeEnum.per_seat_per_year;
                } else if(orderInfo.getPeriodType().equals("ONCE")) {
                    //实施服务包按次数
                    pricePlanType = PricePlanTypeEnum.permanent_count;
                    Map<String, Object> orderTimeObject = (Map<String, Object>) orderInfo.getComponents().get("ord_time");
                    List<String> orderTimeList = Splitter.on(":").splitToList(orderTimeObject.get("value").toString());
                    if(orderTimeList.get(1).equals("Month")) {
                        orderDays = GlobalValue.DAYS_OF_ONE_MONTH;
                        orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    } else if(orderTimeList.get(1).equals("Year")) {
                        orderDays = GlobalValue.DAYS_OF_ONE_YEAR + 1;
                        orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    }
                }
                if(ConfigCenter.CRM_PRODUCT_NUM.contains(skuId)) {
                    Map<String, Object> accountNumObject = (Map<String, Object>) orderInfo.getComponents().get("account_num");
                    if (accountNumObject.get("value").toString().equals("0")) {
                        pricePlanType = PricePlanTypeEnum.trial;
                        orderDays = GlobalValue.ALLOW_TRIAL_DAYS;
                        orderEndTime = orderInfo.getPaidOn() + orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    }
                }
                break;
            case "RENEW":
                buyType = BuyTypeEnum.renew;
                pricePlanType = PricePlanTypeEnum.per_seat_per_year;
                orderDays = GlobalValue.DAYS_OF_ONE_YEAR + 1;
                OrderInfoEntity latestOrder = orderInfoManager.getLatestOrder(outEa);
                orderStartTime = latestOrder.getEndTime().getTime();
                if(orderInfo.getPeriodType().equals("YEAR")) {
                    orderEndTime = orderStartTime + orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                }
                if(ConfigCenter.CRM_PRODUCT_NUM.contains(skuId)) {
                    Map<String, Object> accountNumObject = (Map<String, Object>) orderInfo.getComponents().get("account_num");
                    if (accountNumObject.get("value").toString().equals("0")) {
                        pricePlanType = PricePlanTypeEnum.trial;
                        orderDays = GlobalValue.ALLOW_TRIAL_DAYS;
                        orderEndTime = orderStartTime + orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    }
                }
                break;
            case "TRIAL":
                buyType = BuyTypeEnum.buy;
                pricePlanType = PricePlanTypeEnum.trial;
                //试用期15天
                orderDays = GlobalValue.ALLOW_TRIAL_DAYS;
                orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                break;
            case "UPGRADE":
                buyType = BuyTypeEnum.upgrade;
                pricePlanType = PricePlanTypeEnum.per_seat_per_year;
                //阿里云升级是升级账号数
                orderDays = GlobalValue.DAYS_OF_ONE_YEAR + 1;
                OrderInfoEntity srcOrder = orderInfoManager.getLatestOrder(outEa);
                LogUtils.info("OrderEventServiceImpl.upgradeInstance,userCount2={}", userCount);
                if(srcOrder.getPricePlanType() == PricePlanTypeEnum.trial) {
                    //0元升级订单，新增人数一定是传过来的值，userCount不变
                    //升级订单后，不再是试用的企业，需要使用新的日期
                    orderStartTime = orderInfo.getPaidOn();
                    orderEndTime = orderStartTime + orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                } else {
                    //新增人数
                    userCount = userCount - srcOrder.getUserCount();
                    orderStartTime = srcOrder.getBeginTime().getTime();
                    orderEndTime = srcOrder.getEndTime().getTime();
                }
                break;
            default:
                LogUtils.info("OrderEventServiceImpl.upgradeInstance,product orderType id not support,orderInfo={}.",orderInfo);
                return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }

        OrderInfoEntity entity = OrderInfoEntity.builder()
                .appId(productCode)
                .orderId(orderModel.getOrderId())
                .channel(ChannelEnum.aliyun)
                .buyType(buyType)
                .orderStatus(OrderStatusEnum.normal)
                .orderDays(orderDays)
                .orderCorpId(outEa)
                .paidCorpId(outEa)
                .price((int)(orderInfo.getPaymentPrice()* 100))
                .payPrice((int)(orderInfo.getPaymentPrice()* 100))
                .pricePlanType(pricePlanType)
                .editionId(skuId)
                .editionName(crmEditionEnum.name())
                .userCount(userCount)
                .orderTime(new Timestamp(orderInfo.getCreatedOn()))
                .payTime(new Timestamp(orderInfo.getPaidOn()))
                .beginTime(new Timestamp(orderStartTime))
                .endTime(new Timestamp(orderEndTime))
                .orderFrom(OrderFromEnum.customer)
                .build();

        //入库
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,entity={}.", JSONObject.toJSONString(entity));
        Result<Integer> result = insertOrUpdateOrderInfo(entity);
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,result={}.",result);

        if(!result.isSuccess()) {
            LogUtils.info("OrderEventServiceImpl.upgradeInstance,insertOrUpdateOrderInfo is fail,orderInfo={}.",orderInfo);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }

        Result<Void> openEnterpriseResult = openEnterprise(entity, orderInfo);
        LogUtils.info("OrderEventServiceImpl.upgradeInstance,openEnterpriseResult={}.",openEnterpriseResult);
        if(!openEnterpriseResult.isSuccess()) {
            LogUtils.info("OrderEventServiceImpl.upgradeInstance,openEnterpriseResult id not support,orderInfo={}.",orderInfo);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }

//        if(orderModel.getAction().equals("createInstance") && entity.getAppId().equals("cmgj00055118")) {
//            createOrderResult.setInstanceId(orderModel.getOrderBizId());
//            JSONObject appInfo = new JSONObject();
//            appInfo.put("authUrl", ConfigCenter.CRM_DOMAIN + "/aliyun/market/login");
//            createOrderResult.setAppInfo(appInfo.toJSONString());
//        }

        LogUtils.info("OrderEventServiceImpl.upgradeInstance,createOrderResult={}.",createOrderResult);
        return new Result<>(createOrderResult);
    }

    @Override
    public Result<Integer> insertOrUpdateOrderInfo(OrderInfoEntity entity) {
        return new Result<>(orderInfoManager.insertOrUpdateOrderInfo(entity));
    }

    @Override
    public Result<Void> openEnterprise(OrderInfoEntity entity, DescribeOrderResponseBody orderInfo) {
        String source = CustomerSourceEnum.DINGDING.getSource();

        int userCount = entity.getUserCount();
        if(entity.getBuyType()==BuyTypeEnum.renew) {
            userCount = 0;
        }

        //查看企业是否已开通
        String fsEa;
        List<EnterpriseBindEntity> enterpriseBindList;
        if(entity.getAppId().equals("cmfw00059470")) {
            enterpriseBindList = enterpriseBindManager.getEnterpriseBindListByUid(orderInfo.getAliUid().toString());
        } else {
            enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(ChannelEnum.aliyun, entity.getPaidCorpId());
        }
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            fsEa = EnterpriseUtils.genEA2();
        } else {
            fsEa = enterpriseBindList.get(0).getFsEa();
        }
        LogUtils.info("OrderEventServiceImpl.openEnterprise,fsEa={}.", fsEa);

        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(entity.getAppId(), entity.getEditionId());
        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                .enterpriseAccount(fsEa)
                .orderId(entity.getOrderId())
                .orderTime(entity.getPayTime().getTime())
                .orderTpye(entity.getPricePlanType() == PricePlanTypeEnum.trial ? CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_TRY : CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_BUY)
                .build();

        //订单金额单位是 分，需要转换成 元
        BigDecimal orderAmount = BigDecimal.valueOf(entity.getPayPrice() / 100.0);
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                .beginTime(entity.getBeginTime().getTime())
                .endTime(entity.getEndTime().getTime())
                .quantity(1)
                .allResourceCount(userCount)
                .orderAmount(orderAmount + "")
                .productId(versionModel.getProductId())
                .build();

        CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                .crmOrderDetailInfo(orderDetailInfo)
                .crmOrderProductInfo(orderProductInfo)
                .build();

        LogUtils.info("OrderEventServiceImpl.openEnterprise,orderArg={}", orderArg);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            //新购订单，开通全新的纷享企业
            CreateCustomerArg customerArg = CreateCustomerArg.builder()
                    .source(source)
                    .outEid(entity.getPaidCorpId())
                    .enterpriseAccount(fsEa)
                    .managerName("CRM管理员") //ignorei18n
                    .enterpriseName(fsEa)
                    .build();

            LogUtils.info("OrderEventServiceImpl.openEnterprise,customerArg={}.", customerArg);
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.openEnterprise(customerArg, orderArg);
            if(!result.isSuccess()) {
                return Result.newError(result.getCode(), result.getMsg());
            }

            //保存企业绑定关系
            enterpriseBindManager.insert(entity.getChannel(),
                    fsEa,
                    entity.getPaidCorpId(),
                    BindTypeEnum.auto,
                    BindStatusEnum.create);

        } else {
            //升级订单或续订订单，更新纷享企业
            LogUtils.info("OrderEventServiceImpl.openEnterprise,enterpriseBindList={}.", enterpriseBindList);
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(orderArg);
            if(!result.isSuccess()) {
                return Result.newError(result.getCode(), result.getMsg());
            }
        }

        return Result.newSuccess();
    }
}
