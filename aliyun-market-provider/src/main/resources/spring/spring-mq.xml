<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

<!--    <bean id="enterpriseEventListener" class="com.facishare.open.aliyun.market.mq.EnterpriseEventListener"/>-->
<!--    <bean id="fsEventListener" name="fsEventListener" class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-aliyun-market-config"></constructor-arg>-->
<!--        <constructor-arg index="1" value="tenant.name.server"></constructor-arg>-->
<!--        <constructor-arg index="2" value="tenant.consumer.group"></constructor-arg>-->
<!--        <constructor-arg index="3" value="tenant.consume.topic"></constructor-arg>-->
<!--        <constructor-arg index="4" ref="enterpriseEventListener"></constructor-arg>-->
<!--    </bean>-->

</beans>