use `fs-feishu-db`;
create table if not exists tb_department_bind
(
id int auto_increment primary key comment '主键ID',
channel VARCHAR(32) NOT NULL COMMENT '渠道',
fs_ea varchar(32) not null comment '纷享EA',
fs_dep_id varchar(32) not null comment '纷享部门ID',
fs_leader_user_id varchar(32) comment '纷享部门负责人用户ID',
out_ea varchar(64) not null comment '目标企业EA',
out_dep_id varchar(64) not null comment '目标部门ID',
out_leader_user_id varchar(64) comment '目标部门负责人用户ID',
dep_code varchar(64) not null comment '部门编码',
bind_type varchar(16) not null comment '绑定类型',
bind_status varchar(16) not null comment '绑定状态',

create_time timestamp default current_timestamp comment '创建时间',
update_time timestamp default current_timestamp on update current_timestamp comment '更新时间'
) comment '部门绑定表';