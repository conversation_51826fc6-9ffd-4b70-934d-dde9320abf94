//package com.facishare.open.feishu.web.template.outer.msg;
//
//import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
//import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
//import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgTemplate;
//import com.facishare.open.feishu.syncapi.result.Result;
//import com.facishare.open.feishu.syncapi.service.ExternalMsgService;
//import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
//import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 飞书发送消息模板
// * <AUTHOR>
// * @date 20240926
// */
//
//@Slf4j
//@Component
//public class FeishuSendMsgTemplate extends SendMsgTemplate {
//    @Resource
//    private ExternalMsgService externalMsgService;
//
//    @Override
//    public void sendTextMsg(MethodContext context) {
//        log.info("FeishuSendMsgTemplate.sendTextMsg,context={}",context);
//        SendTextMessageArg sendTextMessageArg = context.getData();
//        Result<Void> result = externalMsgService.sendTextMessage(sendTextMessageArg);
//        log.info("FeishuSendMsgTemplate.sendTextMsg,result={}",result);
//        context.setResult(TemplateResult.newSuccess(result));
//    }
//
//    @Override
//    public void sendCardMsg(MethodContext context) {
//        log.info("FeishuSendMsgTemplate.sendCardMsg,context={}",context);
//        SendTextCardMessageArg sendTextCardMessageArg = context.getData();
//        Result<Void> result = externalMsgService.sendTextCardMessage(sendTextCardMessageArg);
//        log.info("FeishuSendMsgTemplate.sendCardMsg,result={}",result);
//        context.setResult(TemplateResult.newSuccess(result));
//    }
//}
