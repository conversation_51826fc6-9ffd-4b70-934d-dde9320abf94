package com.facishare.open.feishu.web.controller.inner.whatsapp;

import com.facishare.open.feishu.syncapi.base.FsBase;
import com.facishare.open.feishu.syncapi.data.whatsapp.*;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.whatsapp.*;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappEnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappSendMsgService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappTemplateService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 内部服务调用类
 * <AUTHOR>
 * @date 20231227
 */
@Deprecated
@RestController
@RequestMapping(value="/erpdss/API/v1/rest/inner/whatsapp/internal")
public class WhatsappInternalController {
    @Resource
    private WhatsappEnterpriseBindService whatsappEnterpriseBindService;
    @Resource
    private WhatsappTemplateService whatsappTemplateService;
    @Resource
    private WhatsappSendMsgService whatsappSendMsgService;
    @Resource
    private WhatsappCommonService whatsappCommonService;

    @RequestMapping(value = "/enterprise/save", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> saveEnterpriseBindInfo(@RequestBody WhatsappBindInfo whatsappBindInfo) {
        LogUtils.info("InternalController.saveEnterpriseBindInfo.fsEa={},whatsappBindInfo={}", whatsappBindInfo.getFsEa(), whatsappBindInfo);
        //检查参数
        if(ObjectUtils.isEmpty(whatsappBindInfo) || StringUtils.isAnyEmpty(whatsappBindInfo.getFsEa(), whatsappBindInfo.getAppKey(), whatsappBindInfo.getAccessKey(), whatsappBindInfo.getAccessSecret())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappEnterpriseBindService.saveEnterpriseBindInfo(whatsappBindInfo);
    }

    @RequestMapping(value = "/enterprise/query", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappBindInfo> queryEnterpriseBindInfo(@RequestBody FsBase fsBase) {
        LogUtils.info("InternalController.queryEnterpriseBindInfo.fsBase={}", fsBase);
        //检查参数
        if(ObjectUtils.isEmpty(fsBase) || StringUtils.isEmpty(fsBase.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappEnterpriseBindService.queryEnterpriseBindInfo(fsBase.getFsEa());
    }

    @RequestMapping(value = "/enterprise/queryAllPhone", method = RequestMethod.POST)
    @ResponseBody
    public Result<Object> queryAllPhone(@RequestBody FsBase fsBase) {
        //检查参数
        if(ObjectUtils.isEmpty(fsBase) || StringUtils.isEmpty(fsBase.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappEnterpriseBindService.getEnterpriseAllPhone(fsBase.getFsEa());
    }

    @RequestMapping(value = "/template/query", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappGetTemplateResult> getTemplate(@RequestBody WhatsappGetTemplate getTemplate) {
        //检查参数
        if(ObjectUtils.isEmpty(getTemplate) || StringUtils.isEmpty(getTemplate.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappTemplateService.getTemplateData(getTemplate);
    }

    @RequestMapping(value = "/template/create", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappCreateTemplateResult> createTemplate(@RequestBody WhatsappCreateTemplate createTemplate) {
        //检查参数
        if(ObjectUtils.isEmpty(createTemplate) || StringUtils.isEmpty(createTemplate.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappTemplateService.createTemplateData(createTemplate);
    }

    @RequestMapping(value = "/template/update", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappUpdateTemplateResult> updateTemplate(@RequestBody WhatsappUpdateTemplate updateTemplate) {
        //检查参数
        if(ObjectUtils.isEmpty(updateTemplate) || StringUtils.isEmpty(updateTemplate.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappTemplateService.updateTemplateData(updateTemplate);
    }

    @RequestMapping(value = "/template/delete", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappDeleteTemplateResult>  deleteTemplate(@RequestBody WhatsappDeleteTemplate deleteTemplate) {
        //检查参数
        if(ObjectUtils.isEmpty(deleteTemplate) || StringUtils.isEmpty(deleteTemplate.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappTemplateService.deleteTemplateData(deleteTemplate);
    }

    @RequestMapping(value = "/msg/send", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappSendMsgResult> sendMsg(@RequestBody WhatsappSendMsg whatsappSendMsg) {
        //检查参数
        if(ObjectUtils.isEmpty(whatsappSendMsg) || StringUtils.isEmpty(whatsappSendMsg.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappSendMsgService.whatsappSendMsg(whatsappSendMsg);
    }

    @RequestMapping(value = "/common/getBalance", method = RequestMethod.POST)
    @ResponseBody
    public Result<WhatsappBalanceResult> getBalance(@RequestBody FsBase fsBase) {
        //检查参数
        if(ObjectUtils.isEmpty(fsBase) || StringUtils.isEmpty(fsBase.getFsEa())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappCommonService.getBalance(fsBase.getFsEa());
    }

    @RequestMapping(value = "/enterprise/update", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> updateEnterpriseBindInfo(@RequestBody WhatsappBindInfo whatsappBindInfo) {
        LogUtils.info("InternalController.saveEnterpriseBindInfo.fsEa={},whatsappBindInfo={}", whatsappBindInfo.getFsEa(), whatsappBindInfo);
        //检查参数
        if(ObjectUtils.isEmpty(whatsappBindInfo) || StringUtils.isAnyEmpty(whatsappBindInfo.getFsEa(), whatsappBindInfo.getAppKey(), whatsappBindInfo.getAccessKey(), whatsappBindInfo.getAccessSecret())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return whatsappEnterpriseBindService.updateEnterpriseBindInfo(whatsappBindInfo);
    }
}