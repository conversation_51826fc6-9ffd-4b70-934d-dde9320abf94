package com.facishare.open.feishu.syncapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum ExternalTodoStatusEnum {
    PENDING(0, "待审批"),
    APPROVED(1, "同意"),
    REJECTED(2, "拒绝"),
    DELETED(3, "已删除"),
    NOT_PUT(4, "不需要同步"),
    CANCELED(5, "已撤回"),
    PROCESSED(6, "已处理"),
    ;

    private Integer status;
    private String describe;
}
