package com.facishare.open.feishu.web.template.outer.event.order;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OrderPaidEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class FeishuOrderPaidEventHandlerTemplate extends OrderPaidEventHandlerTemplate {
    @Resource
    private FeishuOpenEnterpriseHandlerTemplate feishuAddOrderEventHandlerTemplate;
    @Resource
    private FeishuSaveOrderHandlerTemplate feishuUpdateOrderEventHandlerTemplate;
    @Resource
    private OrderService orderService;


    @Override
    public void openEnterpriseOrSaveOrder(MethodContext context) {
        log.info("FeishuOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context={}",context);
        FeishuOrderPaidEvent event = context.getData();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = orderService.getEnterpriseBindList(event.getTenantKey()).getData();
        log.info("FeishuOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,enterpriseBindList={}",enterpriseBindList);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            //如果没有绑定关系，触发开通企业的逻辑
            context.setResult(TemplateResult.newSuccess(OPEN_ENTERPRISE));
        } else {
            //如果有企业绑定关系，触发保存订单的逻辑
            context.setResult(TemplateResult.newSuccess(SAVE_ORDER));
        }
        log.info("FeishuOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context.2={}",context);
    }

    @Override
    public void openEnterprise(MethodContext context) {
        log.info("FeishuOrderPaidEventHandlerTemplate.openEnterprise,context={}",context);
        TemplateResult result = feishuAddOrderEventHandlerTemplate.execute(context.getData());
        context.setResult(result);
        log.info("FeishuOrderPaidEventHandlerTemplate.openEnterprise,result={}",result);
    }

    @Override
    public void saveOrder(MethodContext context) {
        log.info("FeishuOrderPaidEventHandlerTemplate.saveOrder,context={}",context);
        TemplateResult result = feishuUpdateOrderEventHandlerTemplate.execute(context.getData());
        context.setResult(result);
        log.info("FeishuOrderPaidEventHandlerTemplate.saveOrder,result={}",result);
    }
}
