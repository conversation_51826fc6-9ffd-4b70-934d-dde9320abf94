package com.facishare.open.feishu.syncapi.model.event2;

import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import lombok.Data;

import java.io.Serializable;


/**
 * 部门删除事件
 * <AUTHOR>
 * @date 20220818
 */
@Data
public class FeishuContactDepartmentDeletedV3Event implements Serializable {
    private DepartmentData.Department object;
    private OldDepartment oldObject;

    @Data
    public class OldDepartment implements Serializable {
        private DepartmentData.Department.DepartmentStatus status;
        private String openDepartmentId;
    }
}