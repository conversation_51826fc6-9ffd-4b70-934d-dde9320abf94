package com.facishare.open.feishu.syncapi.model.event2;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞书2.0事件模型
 */
@Data
public class FeishuEventModel2<T> implements Serializable {
    private String schema;
    private EventModelHeader header;
    private T event;//具体事件

    @Data
    public static class EventModelHeader implements Serializable {
        private String eventId;//事件的唯一标识
        private String token;//即Verification Token
        private Long createTime;//事件发送的时间
        private String eventType;//事件类型
        private String tenantKey;//企业标识
        private String appId;//应用ID
    }

    public static void main(String[] args) {
        String plainText = "{\n" +
                "    \"schema\": \"2.0\",\n" +
                "    \"header\": {\n" +
                "        \"event_id\": \"a069a9c71bd8f82dd1fc5a2fae777842\",// 事件的唯一标识\n" +
                "        \"token\": \"l802sPhp2r1AMSPDTaJc5eYZOEnKyp\",// 即Verification Token \n" +
                "        \"create_time\": \"1615813148349\",//  事件发送的时间\n" +
                "        \"event_type\": \"application.application.visibility.added_v6\", // 事件类型 \n" +
                "        \"tenant_key\": \"2d3aa0bc8d8f575d\", // 企业标识 \n" +
                "        \"app_id\": \"cli_9dbb2dd6f5e0d104\" // 应用ID\n" +
                "    },\n" +
                "    \"event\": {\n" +
                "        \"source\": 1, // 事件来源, 为 1 时代表通过普通成员安装增加可见性\n" +
                "        \"users\": [\n" +
                "            {\n" +
                "                \"user_id\": { // 开通的用户 id\n" +
                "                    \"open_id\": \"ou_f370aea2baf6ffc69a6762d31cfaf96a\",\n" +
                "                    \"union_id\": \"on_6bc1e6c0e9ad8193fa4391278eb76891\"\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        FeishuEventModel2 feishuEventModel2 = JSON.parseObject(plainText,FeishuEventModel2.class);
        FeishuAppVisibleAddEvent feishuAppVisibleAddEvent = JSON.parseObject(feishuEventModel2.getEvent().toString(),FeishuAppVisibleAddEvent.class);
        System.out.println(feishuAppVisibleAddEvent);
    }
}