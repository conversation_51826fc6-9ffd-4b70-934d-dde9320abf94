package com.facishare.open.feishu.web.template.outer.event.ticket;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.ticket.TicketEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppTicketEvent;
import com.facishare.open.feishu.web.handler.FeishuEventHandler;
import com.facishare.open.feishu.web.template.model.FeishuEventHandleModel;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 飞书ticket事件处理器模板实现类
 * <AUTHOR>
 * @date 2024-08-19
 */

@Slf4j
@Component
public class FeishuTicketEventHandlerTemplate extends TicketEventHandlerTemplate {
    @Resource
    private RedisDataSource redisDataSource;

    @Override
    public void onTicketEvent(MethodContext context) {
        log.info("FeishuTicketEventHandlerTemplate.onTicketEvent,context={}",context);

        FeishuEventHandleModel eventHandleModel = context.getData();

        FeishuAppTicketEvent feishuAppTicketEvent = JSON.parseObject(eventHandleModel.getEventData(), FeishuAppTicketEvent.class);

        redisDataSource.getRedisClient().set("feishu-" + feishuAppTicketEvent.getAppId() + "-appticket", feishuAppTicketEvent.getAppTicket());

        context.setResult(TemplateResult.newSuccess(FeishuEventHandler.SUCCESS));
    }
}
