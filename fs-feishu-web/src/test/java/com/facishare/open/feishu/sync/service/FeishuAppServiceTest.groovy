package com.facishare.open.feishu.sync.service

import com.facishare.open.feishu.syncapi.arg.QueryTenantAccessTokenArg
import com.facishare.open.feishu.syncapi.service.FeishuAppService
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class FeishuAppServiceTest extends Specification {
    @Resource
    private FeishuAppService feishuAppService;
    def "getBalance"() {
        given:
        QueryTenantAccessTokenArg arg = new QueryTenantAccessTokenArg()
        arg.setAppId("cli_a3ddeb52763b100c")
        arg.setOutEa("100d08b69448975d")
        expect:
        def a = feishuAppService.getTenantAccessToken("feishu", arg)
        print(a)
    }
}
