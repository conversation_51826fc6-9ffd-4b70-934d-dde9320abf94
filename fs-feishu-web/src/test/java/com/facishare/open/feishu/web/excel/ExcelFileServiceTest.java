package com.facishare.open.feishu.web.excel;

import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.enums.TemplateTypeEnum;
import com.facishare.open.feishu.web.excel.service.ExcelFileService;
import org.junit.Test;

import javax.annotation.Resource;

public class ExcelFileServiceTest extends BaseTest {
    @Resource
    private ExcelFileService excelFileService;

    @Test
    public void buildExcelTemplate() {
        Result<BuildExcelFile.Result> result = excelFileService.buildExcelTemplate("82777",
                TemplateTypeEnum.DEPARTMENT_EMPLOYEE_BIND);
        System.out.println(result);
    }

    @Test
    public void importExcelFile() {
        ImportExcelFile.MappingDataArg arg = new ImportExcelFile.MappingDataArg();
        arg.setFsEa("88521");
        arg.setOutEa("100d08b69448975d");
        arg.setFsUserId(1046);
        arg.setTemplateType(TemplateTypeEnum.DEPARTMENT_EMPLOYEE_BIND);
        arg.setNpath("TN_e4772c36158a4fcb80302127eaa5cffe");
        try {
            Result<ImportExcelFile.Result> result = excelFileService.importExcelFile(arg);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
