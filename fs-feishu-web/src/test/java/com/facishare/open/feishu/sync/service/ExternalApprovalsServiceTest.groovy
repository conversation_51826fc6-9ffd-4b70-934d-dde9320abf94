package com.facishare.open.feishu.sync.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTemplate
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService
import com.google.common.collect.Lists
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class ExternalApprovalsServiceTest extends Specification {
    @Resource
    private ExternalApprovalsService externalApprovalsService

    def "createExternalApprovalsTemplate"() {
        given:
        ExternalApprovalsTemplate template = new ExternalApprovalsTemplate()
        template.setApprovalName("@i18n@1")
        template.setApprovalCode("fxiaoke_crm_approval_code_003")
        template.setGroupName("@i18n@2")
        template.setGroupCode("fxiaoke_crm_group_code_003")
        template.setDescription("@i18n@3")
        ExternalApprovalsTemplate.External external = new ExternalApprovalsTemplate.External()
        external.setBizName("@i18n@4")
        external.setSupportPc(Boolean.TRUE)
        external.setSupportMobile(Boolean.TRUE)
        external.setSupportBatchRead(Boolean.FALSE)
        external.setCreateLinkPc()
        external.setCreateLinkMobile()
        template.setExternal(external)
        ExternalApprovalsTemplate.I18nResource resource1 = new ExternalApprovalsTemplate.I18nResource()
        resource1.setLocale("zh-CN")
        resource1.setIsDefault(true)
        ExternalApprovalsTemplate.I18nResource.Text text1 = new ExternalApprovalsTemplate.I18nResource.Text()
        text1.setKey("@i18n@1")
        text1.setValue("这个是ApprovalName2")
        ExternalApprovalsTemplate.I18nResource.Text text2 = new ExternalApprovalsTemplate.I18nResource.Text()
        text2.setKey("@i18n@2")
        text2.setValue("这个是GroupName2")
        ExternalApprovalsTemplate.I18nResource.Text text3 = new ExternalApprovalsTemplate.I18nResource.Text()
        text3.setKey("@i18n@3")
        text3.setValue("这个是Description2")
        ExternalApprovalsTemplate.I18nResource.Text text4 = new ExternalApprovalsTemplate.I18nResource.Text()
        text4.setKey("@i18n@4")
        text4.setValue("这个是BizName2")
        resource1.setTexts(Lists.newArrayList(text1, text2, text3, text4))
        ExternalApprovalsTemplate.Viewer viewer = new ExternalApprovalsTemplate.Viewer()
        viewer.setViewerType("NONE")
        template.setViewers(Lists.newArrayList(viewer))
        template.setI18nResources(Lists.newArrayList(resource1))

        expect:
        def a = externalApprovalsService.createExternalApprovalsTemplate("100d08b69448975d", "cli_a3ddeb52763b100c", UserIdTypeEnum.open_id, DepartmentIdTypeEnum.open_department_id, template)
        print(a)
    }

    def "syncExternalApprovals"() {
        given:
//        String text = "{\"approval_code\":\"83B9FFD7-1936-4F5C-9F6E-7628A3DC4860\",\"status\":\"PENDING\",\"instance_id\":\"testsourceId009\",\"links\":{\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1BY2NvdW50T2JqJmlkPTY1YTc4NDg4OTdhM2JiMDAwNzhlMDI2MSZlYT04NTkwMyZlYT04NTkwMw%3D%3D%26fsEa%3D85903&app_id=cli_a3ddeb52763b100c&state=cli_a3ddeb52763b100c\",\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1BY2NvdW50T2JqJmlkPTY1YTc4NDg4OTdhM2JiMDAwNzhlMDI2MSZlYT04NTkwMyZlYT04NTkwMw%3D%3D%26fsEa%3D85903&app_id=cli_a3ddeb52763b100c&state=cli_a3ddeb52763b100c\"},\"title\":\"@i18n@1\",\"form\":[{\"name\":\"@i18n@2\",\"value\":\"@i18n@3\"},{\"name\":\"@i18n@4\",\"value\":\"@i18n@5\"}],\"start_time\":\"1705906320000\",\"end_time\":\"0\",\"update_time\":\"1705906320000\",\"display_method\":\"NORMAL\",\"task_list\":[{\"task_id\":\"testsourceId009_ou_0fd979c697c5dd375d12ffb999492a91\",\"open_id\":\"ou_0fd979c697c5dd375d12ffb999492a91\",\"links\":{\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1vYmplY3RfOHNRNG1fX2MmaWQ9NjMxNzEwYTU2ZDUxNjkwMDAxZjIxYTEzJmVhPTc0ODYwJmVhPTc0ODYw%26fsEa%3D74860&app_id=cli_a3ddeb52763b100c&state=cli_a3ddeb52763b100c\",\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1vYmplY3RfOHNRNG1fX2MmaWQ9NjMxNzEwYTU2ZDUxNjkwMDAxZjIxYTEzJmVhPTc0ODYwJmVhPTc0ODYw%26fsEa%3D74860&app_id=cli_a3ddeb52763b100c&state=cli_a3ddeb52763b100c\"},\"status\":\"PENDING\",\"create_time\":\"1705906320000\",\"end_time\":\"0\",\"update_time\":\"1705906320000\",\"display_method\":\"NORMAL\"}],\"i18n_resources\":[{\"locale\":\"zh-CN\",\"texts\":[{\"key\":\"@i18n@1\",\"value\":\"待处理的审批流程\"},{\"key\":\"@i18n@2\",\"value\":\"电话\"},{\"key\":\"@i18n@3\",\"value\":\"13055544445\"},{\"key\":\"@i18n@4\",\"value\":\"地点\"},{\"key\":\"@i18n@5\",\"value\":\"你猜猜\"}],\"is_default\":true}]}";
        String text = "{\"approval_code\":\"F9B8BC69-38A3-4B49-B108-4AF7803334DA\",\"status\":\"PENDING\",\"instance_id\":\"testsourceId009\",\"links\":{\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1BY2NvdW50T2JqJmlkPTY1YTc4NDg4OTdhM2JiMDAwNzhlMDI2MSZlYT04NTkwMyZlYT04NTkwMw%3D%3D%26fsEa%3D85903&amp;app_id=cli_a3ddeb52763b100c&amp;state=cli_a3ddeb52763b100c\",\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1BY2NvdW50T2JqJmlkPTY1YTc4NDg4OTdhM2JiMDAwNzhlMDI2MSZlYT04NTkwMyZlYT04NTkwMw%3D%3D%26fsEa%3D85903&amp;app_id=cli_a3ddeb52763b100c&amp;state=cli_a3ddeb52763b100c\"},\"title\":\"@i18n@1\",\"form\":[{\"name\":\"@i18n@2\",\"value\":\"@i18n@3\"},{\"name\":\"@i18n@4\",\"value\":\"@i18n@5\"}],\"start_time\":\"1705906320000\",\"end_time\":\"0\",\"update_time\":\"1705997800000\",\"display_method\":\"NORMAL\",\"task_list\":[{\"task_id\":\"testsourceId009_ou_e014772b2c679b750a68c6c93e354e9b\",\"open_id\":\"ou_e014772b2c679b750a68c6c93e354e9b\",\"links\":{\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1vYmplY3RfOHNRNG1fX2MmaWQ9NjMxNzEwYTU2ZDUxNjkwMDAxZjIxYTEzJmVhPTc0ODYwJmVhPTc0ODYw%26fsEa%3D74860&amp;app_id=cli_a3ddeb52763b100c&amp;state=cli_a3ddeb52763b100c\",\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fcrm.ceshi112.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL2hjcm0vZmVpc2h1L2Z1bmN0aW9uL3RvZG8%2FYXBpbmFtZT1vYmplY3RfOHNRNG1fX2MmaWQ9NjMxNzEwYTU2ZDUxNjkwMDAxZjIxYTEzJmVhPTc0ODYwJmVhPTc0ODYw%26fsEa%3D74860&amp;app_id=cli_a3ddeb52763b100c&amp;state=cli_a3ddeb52763b100c\"},\"status\":\"PENDING\",\"create_time\":\"1705906320000\",\"end_time\":\"0\",\"update_time\":\"1705997800000\",\"display_method\":\"NORMAL\"}],\"i18n_resources\":[{\"locale\":\"zh-CN\",\"texts\":[{\"key\":\"@i18n@1\",\"value\":\"待处理的审批流程\"},{\"key\":\"@i18n@2\",\"value\":\"电话\"},{\"key\":\"@i18n@3\",\"value\":\"13055544445\"},{\"key\":\"@i18n@4\",\"value\":\"地点\"},{\"key\":\"@i18n@5\",\"value\":\"你猜猜\"}],\"is_default\":true}]}";
        ExternalInstancesDetail externalInstancesDetail = JSON.parseObject(text, new TypeReference<ExternalInstancesDetail>(){});
//        externalInstancesDetail.setI18nResources(null)
        externalInstancesDetail.setUpdateMode("UPDATE")
//        externalInstancesDetail.setTitle(null)
//        externalInstancesDetail.setForm(null)
        expect:
        def a = externalApprovalsService.syncExternalApprovals("100d08b69448975d", "cli_a3ddeb52763b100c", externalInstancesDetail)
        print(a)
    }
}
