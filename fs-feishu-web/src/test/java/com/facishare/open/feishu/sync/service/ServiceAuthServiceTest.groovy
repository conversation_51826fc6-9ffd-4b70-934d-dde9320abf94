package com.facishare.open.feishu.sync.service

import com.facishare.open.outer.oa.connector.common.api.info.FsServiceAuthInfo
import com.facishare.open.feishu.syncapi.service.ServiceAuthService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class ServiceAuthServiceTest extends Specification {
    @Resource
    private ServiceAuthService serviceAuthService;
    def "getServiceAuth"() {
        expect:
        def a = serviceAuthService.getServiceAuth("f2af554af6107528e188cd8e6db76fea")
        print(a)
    }

    def "addServiceAuth"() {
        given:
        FsServiceAuthInfo fsServiceAuthInfo = new FsServiceAuthInfo()
        fsServiceAuthInfo.setFsService("4")
        fsServiceAuthInfo.setFsKey("5")
        fsServiceAuthInfo.setFsSecret("6")
        expect:
        def a = serviceAuthService.addServiceAuth(fsServiceAuthInfo)
        print(a)
    }
}
