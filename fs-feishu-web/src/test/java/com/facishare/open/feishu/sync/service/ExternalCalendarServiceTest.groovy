package com.facishare.open.feishu.sync.service


import com.facishare.open.feishu.syncapi.service.ExternalCalendarService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class ExternalCalendarServiceTest extends Specification {
    @Resource
    private ExternalCalendarService externalCalendarService

    def "createExternalCalendar"() {
        expect:
        def a = externalCalendarService.createExternalCalendar(90429, "90429", "66b4781596f1ad0001310cce")
        print(a)
    }

    def "updateExternalCalendar"() {
        expect:
        def a = externalCalendarService.updateExternalCalendar(90429, "90429", "66b4781596f1ad0001310cce")
        print(a)
    }

    def "deleteExternalCalendar"() {
        expect:
        def a = externalCalendarService.deleteExternalCalendar(90429, "90429", "66b4781596f1ad0001310cce")
        print(a)
    }

    def "queryExternalCalendarId"() {
        expect:
        def a = externalCalendarService.queryExternalCalendarId("85903")
        print(a)
    }

    def "queryExternalCalendarEventId"() {
        expect:
        def a = externalCalendarService.queryExternalCalendarEventId("85903", "65bf5d3af5f4410001deed8b")
        print(a)
    }
}
