package com.facishare.open.erpdss.outer.oa.connector.base.context;

import lombok.Data;

import java.io.Serializable;

/**
 * OA基座通用的模板方法Context
 * <AUTHOR>
 * @date 2024-08-19
 */

@Data
public class MethodContext implements Serializable {
    /**
     * 模板方法入参，业务方来定义
     */
    private Object data;

    /**
     * 模板方法返回值，业务方来定义
     */
    private TemplateResult result = new TemplateResult();

    public static MethodContext newInstance(Object data) {
        MethodContext context = new MethodContext();
        context.setData(data);
        return context;
    }

    public boolean isError() {
        if (result == null) return false;
        if (result.isError()) return true;
        return false;
    }

    public boolean isSuccess() {
        if (result == null) return true;
        if (result.isSuccess()) return true;
        return false;
    }

    public <T> T getResultData() {
        return (T) result.getData();
    }
    public <T> T getData() {
        return (T) data;
    }

}
