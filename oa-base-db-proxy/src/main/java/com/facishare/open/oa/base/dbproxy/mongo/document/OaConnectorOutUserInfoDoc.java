package com.facishare.open.oa.base.dbproxy.mongo.document;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;

/**
 * oa连接器人员详情文档模板
 * <AUTHOR>
 * @date 2023/12/06
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class OaConnectorOutUserInfoDoc implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    private ChannelEnum channel;
    private String outEa;
    private String appId;
    private String outUserId;
    private String outUserInfo;
    /**
     * 自建应用需要mobile/email
     *
     */
    private String email;

    private String mobile;

    /**
     * 数据创建时间
     */
    private Long createTime;
    /**
     * 数据更新时间
     */
    private Long updateTime;
}
