package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外部OA人员数据表实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "outer_oa_employee_data", autoResultMap = true)
public class OuterOaEmployeeDataEntity {
    /**
     * 数据库唯一id
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private String id;

    /**
     * 外部企业账号
     */
    private String outEa;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部人员id
     */
    private String outUserId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 外部人员数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject outUserInfo;

    /**
     * 直属部门id
     */
    private String outDeptId;

    /**
     * 动态查询字段1
     */
    private String text1;

    /**
     * 动态查询字段2
     */
    private String text2;

    /**
     * 动态查询字段3
     */
    private String text3;

    /**
     * 动态查询字段4
     */
    private String text4;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;


//    /**
//     * 外部数据人员在多个部门的时候，需要分开提取主属部门，附属部门
//     * outDeptId字段可能存储：
//     * 1. 单个部门ID字符串：如 "11111"
//     * 2. 分号分隔的多个部门ID字符串：如 "1111;aaaa"
//     */
//    public String getMainDeptId(){
//        List<String> deptIds = parseDeptIds();
//        return deptIds.isEmpty() ? null : deptIds.get(0);
//    }
//
//    /**
//     * 获取附属部门ID列表
//     * 返回除主部门外的其他部门ID列表
//     */
//    public List<String> getSubDeptIds(){
//        List<String> deptIds = parseDeptIds();
//        if (deptIds.size() <= 1) {
//            return new ArrayList<>();
//        }
//        // 返回除第一个元素（主部门）外的所有部门ID
//        return new ArrayList<>(deptIds.subList(1, deptIds.size()));
//    }
//
//    /**
//     * 解析outDeptId字段，支持多种格式：
//     * 1. 单个字符串：如 "11111"
//     * 2. 分号分隔字符串：如 "1111;aaaa"
//     */
//    private List<String> parseDeptIds() {
//        String outDeptId = this.getOutDeptId();
//        if (outDeptId == null || outDeptId.trim().isEmpty()) {
//            return new ArrayList<>();
//        }
//
//        outDeptId = outDeptId.trim();
//
//        try {
//            // 检查是否包含分号分隔符
//            if (outDeptId.contains(";")) {
//                return Arrays.asList(outDeptId.split(";"))
//                        .stream()
//                        .map(String::trim)
//                        .filter(s -> !s.isEmpty())
//                        .collect(java.util.stream.Collectors.toList());
//            }
//
//            // 单个部门ID
//            List<String> result = new ArrayList<>();
//            result.add(outDeptId);
//            return result;
//
//        } catch (Exception e) {
//            // 解析失败时，当作单个字符串处理
//            List<String> result = new ArrayList<>();
//            result.add(outDeptId);
//            return result;
//        }
//    }
    }