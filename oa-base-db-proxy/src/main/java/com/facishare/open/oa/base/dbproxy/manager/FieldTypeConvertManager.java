package com.facishare.open.oa.base.dbproxy.manager;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.AccountTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FieldTypeEnum;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Spliterator;

/**
 * 支持字段数据，根据类型转换
 */
@Component
public class FieldTypeConvertManager {

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private ObjectDataManager objectDataManager;


    public Object convert(String dataCenterId,Object sourceValue, FieldTypeEnum fieldTypeEnum,String fsEa){
        switch (fieldTypeEnum){
            case employee:
                OuterOaEmployeeBindEntity oaEmployeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(dataCenterId, null, String.valueOf(sourceValue));
                return oaEmployeeBindEntity == null ? null : Lists.newArrayList(oaEmployeeBindEntity.getFsEmpId());
            case department:
                List<String> deptIds=null;
                if (sourceValue instanceof List){
                     deptIds = (List<String>) sourceValue;

                }else{
                    deptIds=Lists.newArrayList(String.valueOf(sourceValue));
                }
                List<OuterOaDepartmentBindEntity> outerOaDepartmentBindEntities = outerOaDepartmentBindManager.queryNormalOuterOaDepartmentBindEntities(dataCenterId, null, deptIds);
                return outerOaDepartmentBindEntities == null ? null : Lists.newArrayList(outerOaDepartmentBindEntities.stream().map(OuterOaDepartmentBindEntity::getFsDepId));
            case vice_departments:
                List<String> viceDeptIds=null;
                if (sourceValue instanceof List){
                    deptIds = (List<String>) sourceValue;
                }else{
                    deptIds=Lists.newArrayList(String.valueOf(sourceValue));
                }
                List<OuterOaDepartmentBindEntity> outerOaDepartmentBindEntities = outerOaDepartmentBindManager.queryNormalOuterOaDepartmentBindEntities(dataCenterId, null, deptIds);
                return outerOaDepartmentBindEntities == null ? null : Lists.newArrayList(outerOaDepartmentBindEntities.stream().map(OuterOaDepartmentBindEntity::getFsDepId));


            default:
                return sourceValue;
        }
    }

}
