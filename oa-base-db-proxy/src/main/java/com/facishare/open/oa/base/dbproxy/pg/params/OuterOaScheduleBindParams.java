package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaScheduleBindStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaScheduleBindParams
 * 日程绑定参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterOaScheduleBindParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 渠道标识
     */
    private ChannelEnum channel;

    /**
     * 数据中心ID
     */
    private String dcId;

    /**
     * 纷享企业标识
     */
    private String fsEa;

    /**
     * 外部企业标识
     */
    private String outEa;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 纷享日程ID
     */
    private String fsScheduleId;

    /**
     * 外部日程ID
     */
    private String outScheduleId;

    /**
     * 推送的外部人员ID
     */
    private String outUserId;

    /**
     * 状态
     */
    private OuterOaScheduleBindStatusEnum status;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 更新时间（long类型时间戳）
     */
    private Long updateTime;
} 