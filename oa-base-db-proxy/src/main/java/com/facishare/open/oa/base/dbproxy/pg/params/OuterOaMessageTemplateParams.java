package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageTemplateStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaMessageTemplateParams
 * 消息模板参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterOaMessageTemplateParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部企业ea
     */
    private String outEa;

    /**
     * 消息实例模板ID
     */
    private String templateId;

    /**
     * 状态
     */
    private OuterOaMessageTemplateStatusEnum status;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    private Long updateTime;
} 