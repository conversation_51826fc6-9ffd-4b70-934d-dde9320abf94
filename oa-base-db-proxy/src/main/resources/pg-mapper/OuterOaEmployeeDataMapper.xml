<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper">
    
    <!-- 分页查询未绑定员工 -->
    <select id="queryUnboundEmployeesByField" resultType="com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity">
        SELECT a.* FROM outer_oa_employee_data a
        LEFT JOIN outer_oa_employee_bind b ON a.out_user_id = b.out_emp_id
        AND a.out_ea = b.out_ea AND a.channel = b.channel AND a.app_id = b.app_id
        <if test="params.fsEa != null and params.fsEa != ''">AND b.fs_ea = #{params.fsEa}</if>
        WHERE b.out_emp_id IS NULL
        AND a.out_ea = #{params.outEa}
        AND a.channel = #{params.channel}
        AND a.app_id = #{params.appId}
        <if test="params.outUserId != null and params.outUserId != ''">AND a.out_user_id = #{params.outUserId}</if>
        <if test="params.outDeptId != null and params.outDeptId != ''">AND a.out_dept_id = #{params.outDeptId}</if>
        <if test="params.text1 != null and params.text1 != ''">AND a.text1 = #{params.text1}</if>
        <if test="params.text2 != null and params.text2 != ''">AND a.text2 = #{params.text2}</if>
        <if test="params.text3 != null and params.text3 != ''">AND a.text3 = #{params.text3}</if>
        <if test="params.text4 != null and params.text4 != ''">AND a.text4 = #{params.text4}</if>
        ORDER BY a.create_time DESC
    </select>
</mapper> 