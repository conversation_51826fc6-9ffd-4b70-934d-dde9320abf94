<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.open.oa.base.dbproxy.pg.dao.OuterOaTenantConfigDao">
    <resultMap id="BaseResultMap" type="com.facishare.open.oa.base.dbproxy.pg.model.OuterOaTenantConfig">
        <id column="id" property="id"/>
        <result column="channel" property="channel"/>
        <result column="dc_id" property="dcId"/>
        <result column="out_ea" property="outEa"/>
        <result column="fs_ea" property="fsEa"/>
        <result column="app_id" property="appId"/>
        <result column="type" property="type"/>
        <result column="config_info" property="configInfo"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, channel, dc_id, out_ea, fs_ea, app_id, type, config_info, create_time, update_time
    </sql>

    <insert id="save" parameterType="com.facishare.open.oa.base.dbproxy.pg.model.OuterOaTenantConfig">
        INSERT INTO outer_oa_tenant_config (
            channel, dc_id, out_ea, fs_ea, app_id, type, config_info, create_time, update_time
        ) VALUES (
            #{channel}, #{dcId}, #{outEa}, #{fsEa}, #{appId}, #{type}, #{configInfo}, #{createTime}, #{updateTime}
        )
    </insert>

    <update id="update" parameterType="com.facishare.open.oa.base.dbproxy.pg.model.OuterOaTenantConfig">
        UPDATE outer_oa_tenant_config
        SET config_info = #{configInfo},
            update_time = #{updateTime}
        WHERE channel = #{channel}
          AND out_ea = #{outEa}
          AND fs_ea = #{fsEa}
          AND app_id = #{appId}
          AND type = #{type}
    </update>

    <select id="getConfig" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM outer_oa_tenant_config
        WHERE channel = #{channel}
          AND out_ea = #{outEa}
          AND fs_ea = #{fsEa}
          AND app_id = #{appId}
          AND type = #{type}
        LIMIT 1
    </select>
</mapper> 