package com.facishare.open.ding.provider.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.vo.AccountVo;
import com.facishare.open.ding.api.vo.CreateCrmEmployeeVo;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.provider.model.InnerSearchQueryInfo;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/2/23 19:45
 * @Version 1.0
 */
@Service
@Slf4j
public class CrmRestManager {

    /**
     * 创建部门接口
     * @param ei 企业ID
     * @param name 部门名称
     * @param parentId 父部门ID
     * @param deptOwner 部门负责人ID
     * @return 创建结果
     */
    public Result<Integer> createDept(Integer ei, String name, Integer parentId, Integer deptOwner) {
        String crmParentId = Objects.isNull(parentId) ? "99999" : parentId.toString();
        Map<String,String> hearsMap = Maps.newHashMap();
        hearsMap.put("x-fs-ei", ei.toString());
        hearsMap.put("x-fs-userinfo", "-10000");

        Map<String,Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "DepartmentObj");
        objectData.put("name", name);
        objectData.put("record_type", "default__c");
        objectData.put("status", "0");
        objectData.put("manager_id", Objects.isNull(deptOwner) ? null : Lists.newArrayList(deptOwner.toString()));
        objectData.put("parent_id", Lists.newArrayList(crmParentId));
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.createDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code = Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message = JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, name:{}, parentId:{}, deptOwner:{}, crm createDept message:{}", ei, name, parentId, deptOwner, httpResponseMessage.getContent());
        if (ResultCode.SUCCESS.getErrorCode().equals(code)) {
            Object deptId = JSONPath.read(httpResponseMessage.getContent(), "$.data.objectData._id");
            return Result.newSuccess(Integer.parseInt(deptId.toString()));
        }
        return Result.newError(code, message);
    }

    /**
     * 创建部门接口
     * @deprecated 使用 {@link #createDept(Integer, String, Integer, Integer)} 替代
     */
    @Deprecated
    public Result<Integer> createDept(DeptVo vo) {
        return createDept(vo.getEi(), vo.getName(), vo.getCrmParentId(), vo.getCrmDeptOwner());
    }

    /**
     * 修改部门接口
     * @param ei 企业ID
     * @param crmDeptId CRM部门ID
     * @param name 部门名称
     * @param crmParentId CRM父部门ID
     * @param crmDeptOwner CRM部门负责人ID
     * @return 修改结果
     */
    public Result<Void> modifyDept(Integer ei, Integer crmDeptId, String name, Integer crmParentId, Integer crmDeptOwner) {
        Map<String,String> hearsMap = Maps.newHashMap();
        hearsMap.put("x-fs-ei", ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData = Maps.newHashMap();
        Map<String, Object> modifyMap = Maps.newHashMap();
        objectData.put("_id", crmDeptId);
        objectData.put("name", name);
        objectData.put("record_type", "default__c");
        objectData.put("status", "0");
        if(ObjectUtils.isNotEmpty(crmDeptOwner)) {
            //负责人
            objectData.put("manager_id", Lists.newArrayList(crmDeptOwner.toString()));
        }
        String parentId = Objects.isNull(crmParentId) ? "99999" : crmParentId.toString();
        objectData.put("parent_id", Lists.newArrayList(parentId));

        modifyMap.put("data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.updateDeptUrl(), hearsMap, JSONObject.toJSONString(modifyMap));
        Integer code = Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message = JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, crmDeptId:{}, name:{}, crmParentId:{}, crmDeptOwner:{}, crm modifyDept message:{}", 
                ei, crmDeptId, name, crmParentId, crmDeptOwner, httpResponseMessage.getContent());
        return Result.newError(code, message);
    }

    /**
     * 停用部门接口
     */
    public Result<Void> stopDept(DeptVo vo){
        Integer ei=vo.getEi();
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("dataIds", Lists.newArrayList(vo.getCrmDeptId().toString()));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.stopDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, vo:{}, crm stop message:{}",vo.getEi(),vo,httpResponseMessage.getContent());

        return Result.newError(code,message);
    }

    /**
     * 创建员工
     */
    public Result<Integer> createEmp(CreateCrmEmployeeVo vo){
        Integer ei=vo.getEi();
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");

        objectData.put("name",vo.getName());
        objectData.put("full_name",vo.getName());
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        objectData.put("sex","M");
        if(StringUtils.isNotEmpty(vo.getCrmMainDeptId())) {
            objectData.put("main_department",Lists.newArrayList(vo.getCrmMainDeptId()));
        }
        if(StringUtils.isNotEmpty(vo.getMobile())) {
            objectData.put("phone",vo.getMobile());
            //登录账号
            objectData.put("user_name",vo.getMobile());
        }
        //附属部门
        if(CollectionUtils.isNotEmpty(vo.getCrmViceDepts())) {
            objectData.put("vice_departments",vo.getCrmViceDepts());
        }
        if(ObjectUtils.isNotEmpty(vo.getLeader())){
            objectData.put("leader",Lists.newArrayList(vo.getLeader().toString()));
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.createUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();

        log.info("trace crm createEmp message:{}",httpResponseMessage.getContent());
        if(ResultCode.SUCCESS.getErrorCode().equals(code)){
            Object emp = JSONPath.read(httpResponseMessage.getContent(),  "$.data.objectData.user_id");
            return Result.newSuccess(Integer.parseInt(emp.toString()));
        }
        return Result.newError(code,message);
    }


    /**
     * 修改员工
     */
    public Result<Integer> modifyEmp(CreateCrmEmployeeVo vo){
        //线上环境user_id与_id不一致
        Integer ei=vo.getEi();
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");
        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");
        objectData.put("_id",vo.getCrmEmpId().toString());
        objectData.put("name",vo.getName());
        objectData.put("full_name",vo.getName());
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        if(StringUtils.isNotEmpty(vo.getCrmMainDeptId())) {
            objectData.put("main_department",Lists.newArrayList(vo.getCrmMainDeptId()));
        }
        if(StringUtils.isNotEmpty(vo.getMobile())) {
            objectData.put("phone",vo.getMobile());
            //登录账号
            objectData.put("user_name",vo.getMobile());
        }
        //附属部门
        if(CollectionUtils.isNotEmpty(vo.getCrmViceDepts())) {
            objectData.put("vice_departments",vo.getCrmViceDepts());
        }
        if(ObjectUtils.isNotEmpty(vo.getLeader())){
            objectData.put("leader",Lists.newArrayList(vo.getLeader().toString()));
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.modifyUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace crm modifyEmp vo:{},message:{}",vo,httpResponseMessage.getContent());
        return Result.newError(code,message);
    }

    /**
     * 停用员工
     */
    public Result<Integer> stopEmp(Integer crmEmpId,Integer enterpriseId){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");
        Object object_id = queryByField(enterpriseId,"user_id",crmEmpId.toString()).getData().get("object_id");
        if(ObjectUtils.isEmpty(object_id))return Result.newSuccess();
        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");
        objectData.put("_id",object_id.toString());
        objectData.put("status","1");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.stopUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace crm stop message:{}",httpResponseMessage.getContent());
        return Result.newError(code,message);
    }

    /**
     * 根据手机号查询对象数据
     */
    public Result<Map<String,Object>> queryByField(Integer enterpriseId,String filed,String filedValue){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);

        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", "PersonnelObj");
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/PersonnelObj"), hearsMap, JSONObject.toJSONString(queryMap));

        String userID = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0].user_id").toString();
        String object_id = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0]._id").toString();
        Map<String, Object> objectMap = Maps.newHashMap();
        objectMap.put("user_id",userID);
        objectMap.put("object_id",object_id);
        return Result.newSuccess(objectMap);
    }

    /**
     * 根据客户字段查询客户对象数据
     * @return
     */
    public Result<AccountVo> queryAccountById(Integer enterpriseId, String filedValue){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name("_id");
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);

        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", "AccountObj");
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/AccountObj"), hearsMap, JSONObject.toJSONString(queryMap));

        String userID = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0].user_id").toString();
        String object_id = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0]._id").toString();
        String name = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0].name").toString();
        AccountVo accountVo=new AccountVo();

        accountVo.setAccountId(filedValue);
        accountVo.setName(name);

        return Result.newSuccess(accountVo);
    }

    //查询对象
    public Result<String> queryCrmObj(Integer enterpriseId,String objName,String filed,String filedValue){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);

        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", objName);
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/" + objName), hearsMap, JSONObject.toJSONString(queryMap));

        if(httpResponseMessage.getStatusCode() != 200) {
            return Result.newError(httpResponseMessage.getStatusCode(), httpResponseMessage.getMessage());
        }
        return Result.newSuccess(httpResponseMessage.getContent());
    }



}
