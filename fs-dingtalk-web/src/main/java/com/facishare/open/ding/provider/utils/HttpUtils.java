package com.facishare.open.ding.provider.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * Created by huangzh on 2018/6/1.
 */
@Slf4j
public class HttpUtils {
    //可以重写httpclient，过滤自己的证书和协议
    private static CloseableHttpClient httpclient = HttpClients.createDefault();
    private static final String CHARSET = "UTF-8";

    public static CloseableHttpResponse httpPost(String url, String jsonArg, Map<String, String> header) {
        HttpPost httppost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(3000).setConnectionRequestTimeout(1000).setSocketTimeout(3000).build();
        httppost.setConfig(requestConfig);

        HttpEntity httpEntity = new StringEntity(jsonArg, CHARSET);
        httppost.setEntity(httpEntity);
        if (header != null) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                httppost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        httppost.setHeader("Content-Type", "application/json");
        log.info("request url:{},request headers:{},request body:{}", url, Lists.newArrayList(httppost.getAllHeaders()).toString(), jsonArg);
        CloseableHttpResponse response = null;
        try {
            response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return response;
            } else {
                if (null != response) {
                    response.close();
                }
                log.warn("request failed,url:{},arg:{},return:{}", url, jsonArg, response.getStatusLine().toString());
            }
        } catch (IOException e) {
//            e.printStackTrace();
            log.warn("response stream exception,{}", e.toString());
            if (null != response) {
                try {
                    response.close();
                } catch (IOException e1) {
//                    e1.printStackTrace();
                    log.error("when response exception close response exception,{}", e1.toString());
                }
            }
        }
        return null;
    }

    public static CloseableHttpResponse httpsGet(String strUrl) {
//        URL url = new URL(strUrl);
//        URI uri = new URI(url.getProtocol(), url.getHost(), url.getPath(), url.getQuery(), null);
//        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response;
        try {
            URL url = new URL(strUrl);
            URI uri = new URI(url.getProtocol(), url.getHost(), url.getPath(), url.getQuery(), null);
            HttpGet httpGet = new HttpGet(uri);
            @SuppressWarnings("deprecation")
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                //信任所有
                @Override
                public boolean isTrusted(X509Certificate[] chain,
                                         String authType) throws CertificateException {
                    return true;
                }
            }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
            CloseableHttpClient client =  HttpClients.custom().setSSLSocketFactory(sslsf).build();

            response = client.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return response;
            } else {
                log.error("httpGet request failed,url:{},return:{}", url, response.getStatusLine().toString());
            }
        } catch (Exception e) {
            log.error("httpGet response stream exception,{}", e.toString());
        }
        return null;
    }

    public static CloseableHttpResponse httpPostDataCenter(String url, String jsonArg, Map<String, String> header) {
        HttpPost httppost = new HttpPost(url);
        //请求超时
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(3000).setConnectionRequestTimeout(1000).setSocketTimeout(3000).build();
        httppost.setConfig(requestConfig);

        HttpEntity httpEntity = new StringEntity(jsonArg, CHARSET);
        httppost.setEntity(httpEntity);
        if (header != null) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                httppost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        httppost.setHeader("Content-Type", "application/json");
        log.info("request url:{},request headers:{},request body:{}", url, Lists.newArrayList(httppost.getAllHeaders()).toString(), jsonArg);
        CloseableHttpResponse response;
        try {
            response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return response;
            } else {
                if (null != response) {
                    response.close();
                }
                log.warn("request failed,url:{},arg:{},return:{}", url, jsonArg, response.getStatusLine().toString());
            }
        } catch (IOException e) {
//            e.printStackTrace();
            log.warn("response stream exception,{}", e.toString());
        }
        return null;
    }

    private static CloseableHttpResponse httpGet(String url, Map map) {
        return null;
    }
}
