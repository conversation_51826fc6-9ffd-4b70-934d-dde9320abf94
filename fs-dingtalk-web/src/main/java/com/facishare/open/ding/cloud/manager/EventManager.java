package com.facishare.open.ding.cloud.manager;

import com.facishare.open.ding.api.enums.BizServiceEnum;
import com.facishare.open.ding.api.service.PollingSyncService;
import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.cloud.utils.SpringUtils;
import com.facishare.open.ding.common.result.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/5/5 23:51 处理事件类型
 * @Version 1.0
 */
@Service
@Slf4j
public class EventManager {

    @Autowired
    private SpringUtils springUtils;

    public Map<Integer, DingEventService> serviceMap = Maps.newHashMap();
    private static ExecutorService executorService = new ThreadPoolExecutor(10, 20, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue(100));
    @Autowired
    private PollingSyncService pollingSyncService;

    @PostConstruct
    public void init() {
        for (BizServiceEnum bizServiceEnum : BizServiceEnum.values()) {
            DingEventService service = (DingEventService) springUtils.getApplicationContext().getBean(bizServiceEnum.getBeanName());
            serviceMap.put(bizServiceEnum.getBizType(), service);
        }
    }

    public Result<Void> doProcessEvent(List<HighBizDataVo> bizDataVos) {
        for (HighBizDataVo bizDataVo : bizDataVos) {
            DingEventService service = serviceMap.get(bizDataVo.getBizType());
            if (ObjectUtils.isEmpty(service)) continue;
            HighBizDataDo bizDataDo = new HighBizDataDo();
            BeanUtils.copyProperties(bizDataVo, bizDataDo);
            bizDataDo.convertGmtModify(bizDataVo.getGmtModified());
            bizDataDo.convertGmtCrete(bizDataVo.getGmtCreate());
            log.info("copy bizdata:{}", bizDataDo);
            //修改下同步时间
//            Integer eventType= ConfigCenter.HIGH_EVENT_TYPE.contains(bizDataDo.getBizType())?BizDataTypeEnum.HIGH_LEVEL.getType():BizDataTypeEnum.MEDIUM_LEVEL.getType();
//            PollingSyncDataVo pollingSyncDataArg = PollingSyncDataVo.builder().lastSyncTime(bizDataDo.getGmtModified()).eventLevel(eventType).build();
//            pollingSyncService.updateSync(pollingSyncDataArg);
//            log.info("update time arg:{}", pollingSyncDataArg);
            try {
                service.executeEvent(bizDataDo);
            } catch (Exception e) {
                log.warn("deal fail event :{}，message :{}", bizDataDo, e.getMessage());
                e.printStackTrace();
            }

        }
        log.info("doProcess event finish");
        return Result.newSuccess();

    }

}
