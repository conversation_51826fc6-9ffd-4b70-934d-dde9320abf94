package com.facishare.open.ding.api.model.connector;

import lombok.Data;

/**
 * 连接器仓库对象模型
 * <AUTHOR>
 * @date 2021/07/27
 */
@Data
public class WarehouseModel extends BaseConnectorObjectModel {
    private DataModel data = new DataModel();

    @Data
    public static class DataModel {
        private String code;//仓库编码 必填
        private String name;//仓库名称 必填
        private String warehouseCategoryCode;//仓库分类编码
        private String remark;//备注
    }
}
