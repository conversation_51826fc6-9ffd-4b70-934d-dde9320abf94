package com.facishare.open.ding.provider.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv.OrderInfo;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaOrderInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaOrderInfoMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaAppInfoMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaOrderInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseExtendModel;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseTrialInfo;
import com.fxiaoke.api.IdGenerator;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/25 16:28
 * @Version 1.0
 */
@Service("cloudOrderServiceImpl")
@Slf4j
public class CloudOrderServiceImpl implements CloudOrderService {

    @Autowired
    private OuterOaOrderInfoMapper outerOaOrderInfoMapper;

    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private OuterOaAppInfoMapper outerOaAppInfoMapper;

    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AppAuthService appAuthService;
    @Override
    public Integer addOrder(OrderModel orderModel) {
        // 创建新表订单对象
        OuterOaOrderInfoEntity entity = new OuterOaOrderInfoEntity();
        entity.setId(IdGenerator.get());
        entity.setChannel(ChannelEnum.dingding);
        entity.setOrderId(String.valueOf(orderModel.getOrderId()));
        entity.setOrderType(convertToOrderType(orderModel.getOrderType()));
        entity.setPaidOutEa(orderModel.getCorpId());
        entity.setBeginTime(orderModel.getServiceStartTime());
        entity.setEndTime(orderModel.getServiceStopTime());

        final Integer suiteId = Integer.valueOf(orderModel.getSuiteId());
        Result<List<AppAuthResult>> appResult = appAuthService.conditionAppAuth(orderModel.getCorpId(), null, Long.valueOf(suiteId));
        String appId = Optional.ofNullable(appResult)
                .map(Result::getData)
                .map(appAuthResults -> appAuthResults.get(0))
                .map(AppAuthResult::getAppId)
                .map(String::valueOf)
                .orElse(null);
        entity.setAppId(appId);

        // 构建orderInfo字段
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOpenId(orderModel.getOpenId());
        orderInfo.setUnionId(orderModel.getUnionId());
        orderInfo.setSubQuantity(orderModel.getSubQuantity());
        orderInfo.setSuiteId(suiteId);
        orderInfo.setSuiteKey(orderModel.getSuiteKey());
        orderInfo.setGoodName(orderModel.getGoodsName());
        orderInfo.setOrderChargeType(orderModel.getOrderChargeType());
        orderInfo.setMinOfPeople(orderModel.getMinOfPeople());
        orderInfo.setMaxOfPeople(orderModel.getMaxOfPeople());
        orderInfo.setGoodsCode(orderModel.getGoodsCode());
        orderInfo.setItemName(orderModel.getItemName());
        orderInfo.setItemCode(orderModel.getItemCode());
        orderInfo.setPayFee(orderModel.getPayFee());
        orderInfo.setNominalPayFee(orderModel.getNominalPayFee());
        orderInfo.setDiscountFee(orderModel.getDiscountFee());
        orderInfo.setDiscount(orderModel.getDiscount());
        orderInfo.setDistributorCorpId(orderModel.getDistributorCorpId());
        orderInfo.setDistributorCorpName(orderModel.getDistributorCorpName());

        entity.setOrderInfo(JSON.toJSONString(orderInfo));
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return outerOaOrderInfoManager.insert(entity);
    }

    @Override
    public OrderInfoResult queryByOrderId(Long orderId, String appId) {
        // 根据订单ID查询
        LambdaQueryWrapper<OuterOaOrderInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaOrderInfoEntity::getChannel, ChannelEnum.dingding)
                  .eq(OuterOaOrderInfoEntity::getOrderId, String.valueOf(orderId))
                  .eq(OuterOaOrderInfoEntity::getAppId, appId);

        OuterOaOrderInfoEntity entity = outerOaOrderInfoMapper.selectOne(queryWrapper);
        return convertToOrderInfoResult(entity);
    }

    @Override
    public Integer queryCount(Long orderId, String appId) {
        // 根据订单ID查询数量
        LambdaQueryWrapper<OuterOaOrderInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaOrderInfoEntity::getChannel, ChannelEnum.dingding)
                  .eq(OuterOaOrderInfoEntity::getOrderId, String.valueOf(orderId))
                  .eq(OuterOaOrderInfoEntity::getAppId, appId);

        return Math.toIntExact(outerOaOrderInfoMapper.selectCount(queryWrapper));
    }

    @Override
    public List<OrderInfoResult> queryByCorpId(String corpId, String appId) {
        // 根据企业ID查询
        LambdaQueryWrapper<OuterOaOrderInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaOrderInfoEntity::getChannel, ChannelEnum.dingding)
                  .eq(OuterOaOrderInfoEntity::getPaidOutEa, corpId)
                  .eq(OuterOaOrderInfoEntity::getAppId, appId);

        List<OuterOaOrderInfoEntity> entities = outerOaOrderInfoMapper.selectList(queryWrapper);
        return entities.stream()
                .map(this::convertToOrderInfoResult)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public OrderInfoResult queryLastOrder(String corpId, String suiteId) {
        // 查询最新订单
        LambdaQueryWrapper<OuterOaOrderInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaOrderInfoEntity::getChannel, ChannelEnum.dingding)
                  .eq(OuterOaOrderInfoEntity::getPaidOutEa, corpId);

        // 如果指定了suiteId，需要从orderInfo中查找
        if (StringUtils.isNotBlank(suiteId)) {
            List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoMapper.queryAppAuth(
                    ChannelEnum.dingding,
                    corpId,  // 使用corpId作为outEa
                    null,  // 不指定appId
                    Long.valueOf(suiteId)
            );
            if (!appInfoEntities.isEmpty()) {
                OuterOaAppInfoEntity appInfoEntity = appInfoEntities.get(0);
                queryWrapper.eq(OuterOaOrderInfoEntity::getAppId, appInfoEntity.getAppId());
            }
        }
        // 直接按时间排序取最新的
        queryWrapper.orderByDesc(OuterOaOrderInfoEntity::getBeginTime)
                  .last("LIMIT 1");
        OuterOaOrderInfoEntity entity = outerOaOrderInfoMapper.selectOne(queryWrapper);
        return convertToOrderInfoResult(entity);
    }

    @Override
    public List<OrderInfoResult> queryOrderByBetweenTime(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return new ArrayList<>();
        }

        // 根据时间范围查询
        LambdaQueryWrapper<OuterOaOrderInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaOrderInfoEntity::getChannel, ChannelEnum.dingding)
                  .ge(OuterOaOrderInfoEntity::getBeginTime, startTime.getTime())
                  .le(OuterOaOrderInfoEntity::getBeginTime, endTime.getTime());

        List<OuterOaOrderInfoEntity> entities = outerOaOrderInfoMapper.selectList(queryWrapper);
        return entities.stream()
                .map(this::convertToOrderInfoResult)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Integer removeOrder(Long orderId, String ea, String appId) {
        // 删除订单
        LambdaQueryWrapper<OuterOaOrderInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaOrderInfoEntity::getChannel, ChannelEnum.dingding)
                  .eq(OuterOaOrderInfoEntity::getOrderId, String.valueOf(orderId))
                  .eq(OuterOaOrderInfoEntity::getAppId, appId);

        // 如果指定了企业账号，增加额外条件
        if (StringUtils.isNotBlank(ea)) {
            // 获取企业绑定信息
            List<OuterOaEnterpriseBindEntity> binds = outerOaEnterpriseBindManager.queryByFsEa(ChannelEnum.dingding, ea);
            if (!binds.isEmpty()) {
                OuterOaEnterpriseBindEntity bind = binds.get(0);
                queryWrapper.eq(OuterOaOrderInfoEntity::getPaidOutEa, bind.getOutEa());
            }
        }

        return outerOaOrderInfoMapper.delete(queryWrapper);
    }

    @Override
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(String fsEa) {
        if (StringUtils.isBlank(fsEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 查询企业绑定关系
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(
                ChannelEnum.dingding,
                fsEa,
                null,
                String.valueOf(ConfigCenter.APP_CRM_ID)
        );

        if (entity == null) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        EnterpriseTrialInfo enterpriseTrialInfo = new EnterpriseTrialInfo();
        String outEa = entity.getOutEa();

        // 处理扩展信息
        DingConnectorVo connectInfo = new Gson().fromJson(entity.getConnectInfo(), DingConnectorVo.class);
        if (connectInfo != null && StringUtils.isNotEmpty(connectInfo.getExtend())) {
            enterpriseTrialInfo.setExtend(connectInfo.getExtend());
        } else {
            String enterpriseExtend = GlobalValue.enterprise_extend;
            enterpriseTrialInfo.setExtend(enterpriseExtend);
        }

        // 更新扩展信息中的首次登陆状态
        Gson gson = new Gson();
        EnterpriseExtendModel extendModel = gson.fromJson(enterpriseTrialInfo.getExtend(), EnterpriseExtendModel.class);
        if (extendModel != null && extendModel.getIsFirstLand() == Boolean.TRUE) {
            // 更新至数据库
            extendModel.setIsFirstLand(Boolean.FALSE);
            if (connectInfo == null) {
                connectInfo = new DingConnectorVo();
            }
            connectInfo.setExtend(gson.toJson(extendModel));
            entity.setConnectInfo(gson.toJson(connectInfo));
            outerOaEnterpriseBindManager.updateById(entity);
        }

        // 查询最新订单
        OrderInfoResult orderInfoResult = this.queryLastOrder(outEa, com.facishare.open.ding.provider.config.ConfigCenter.CRM_SUITE_ID);
        if (orderInfoResult == null) {
            return Result.newError(ResultCode.NOT_ORDER_DATA);
        }

        enterpriseTrialInfo.setBindType(BindTypeEnum.auto);
        enterpriseTrialInfo.setOutEa(outEa);

        //判断该订单是不是试用订单，根据这个字段是否有值，是否等于TRYOUT进行判断，在设计代码初期，有些以前的试用订单没有值，直接忽略
        if(StringUtils.isNotEmpty(orderInfoResult.getOrderChargeType()) && StringUtils.equalsIgnoreCase(orderInfoResult.getOrderChargeType(), "TRYOUT")) {
            enterpriseTrialInfo.setIsTrial(Boolean.TRUE);
            enterpriseTrialInfo.setBeginTime(new Timestamp(orderInfoResult.getServiceStartTime().getTime()));
            enterpriseTrialInfo.setEndTime(new Timestamp(orderInfoResult.getServiceStopTime().getTime()));
        } else {
            enterpriseTrialInfo.setIsTrial(Boolean.FALSE);
        }

        return Result.newSuccess(enterpriseTrialInfo);
    }

    /**
     * 将OuterOaOrderInfoEntity转换为OrderInfoResult
     *
     * @param entity 订单实体
     * @return 订单结果对象
     */
    private OrderInfoResult convertToOrderInfoResult(OuterOaOrderInfoEntity entity) {
        if (entity == null) {
            return null;
        }

        OrderInfoResult result = new OrderInfoResult();

        // 设置基本字段
        result.setCorpId(entity.getPaidOutEa());
        result.setOrderId(Long.valueOf(entity.getOrderId()));
        result.setOrderType(entity.getOrderType().name());

        // 设置时间字段
        if (entity.getBeginTime() != null) {
            result.setServiceStartTime(new Date(entity.getBeginTime()));
        }
        if (entity.getEndTime() != null) {
            result.setServiceStopTime(new Date(entity.getEndTime()));
        }

        // 解析订单详情JSON
        OrderInfo orderInfo = JSON.parseObject(entity.getOrderInfo(), OrderInfo.class);
        if (orderInfo != null) {
            result.setOpenId(orderInfo.getOpenId());
            result.setUnionId(orderInfo.getUnionId());
            result.setSubQuantity(orderInfo.getSubQuantity());
            result.setSuiteId(orderInfo.getSuiteId());
            result.setSuiteKey(orderInfo.getSuiteKey());
            result.setGoodName(orderInfo.getGoodName());
            result.setOrderChargeType(orderInfo.getOrderChargeType());
            result.setMinOfPeople(orderInfo.getMinOfPeople());
            result.setMaxOfPeople(orderInfo.getMaxOfPeople());
            result.setGoodsCode(orderInfo.getGoodsCode());
            result.setItemName(orderInfo.getItemName());
            result.setItemCode(orderInfo.getItemCode());
            result.setPayFee(orderInfo.getPayFee());
            result.setNominalPayFee(orderInfo.getNominalPayFee());
            result.setDiscountFee(orderInfo.getDiscountFee());
            result.setDiscount(orderInfo.getDiscount());
            result.setDistributorCorpId(orderInfo.getDistributorCorpId());
            result.setDistributorCorpName(orderInfo.getDistributorCorpName());
        }

        return result;
    }

    /**
     * 转换订单类型
     *
     * @param orderType 订单类型字符串
     * @return 枚举类型
     */
    private OuterOaOrderInfoTypeEnum convertToOrderType(String orderType) {
        return Arrays.stream(OuterOaOrderInfoTypeEnum.values())
                .filter(item -> item.name().equalsIgnoreCase(orderType))
                .findFirst()
                .orElse(OuterOaOrderInfoTypeEnum.buy);
    }
}
