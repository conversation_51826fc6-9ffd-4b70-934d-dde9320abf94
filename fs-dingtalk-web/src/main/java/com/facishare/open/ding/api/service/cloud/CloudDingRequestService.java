package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/15 14:27 包装第三方应用钉钉的相关接口
 * @Version 1.0
 */
public interface CloudDingRequestService {

    //获取上级部门列表详情
    Result<List<Dept>> queryListParentById(String dingCorpId,Long dingDeptId,String suiteId);


}
