package com.facishare.open.ding.web.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.facishare.open.ding.web.constants.ConfigCenter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/4/23 17:38
 * @Version 1.0
 */
@Slf4j
public class SecurityUtil {
    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(ConfigCenter.BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个aes对象
     */
    @Getter
    private final static AES aes = SecureUtil.aes(SECRET_BYTES);

    /**
     * 对字符串进行加密处理
     * @param str
     * @return
     * @throws Exception
     */
    public static String encryptStr(String str) {
        if(StringUtils.isEmpty(str)) {
            return str;
        }
        //加密  这里我使用自定义的AES加密工具、
        String encryptValue=str;
        try {
            encryptValue = aes.encryptBase64(str);
        } catch (Exception e) {
            log.debug("encrypt value fail:{}",e.getMessage());
        }
        return encryptValue;
    }

    /**
     * 对字符串进行解密处理
     * @param str
     * @return
     * @throws Exception
     */
    public static String decryptStr(String str) {
        if(StringUtils.isEmpty(str)) {
            return str;
        }
        //解密
        String decryptValue=str;
        try {
            decryptValue = aes.decryptStr(str);
        } catch (Exception e) {
            log.debug("decrypt value fail:{}",e.getMessage());
        }
        return decryptValue;
    }

    public static void main(String[] args) {
        String msg = "niweishenmeyaoduzhehangwenbenne";
        String eMsg = encryptStr(msg);
        System.out.println(eMsg);
    }
}
