package com.facishare.open.ding.cloud.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Created by system on 2018/6/1.
 */
@Slf4j
public class DateUtils {

    public static String dateToString(long ms){
        return DateFormatUtils.format(ms, "yyyy-MM-dd");
    }

    public static Long timeStamp(String timeValue){
        //time -> {Date@9266} "Mon May 24 23:42:17 CST 2021"

        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
        Date date = new Date();
        try{
            date = sdf.parse(timeValue);
        }catch (Exception e){
            e.printStackTrace();
        }

        return date.getTime();
    }

    public static Date parseDate(String date,String format) {
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(date,format);
        } catch (Exception e) {
            return null;
        }
    }
}
