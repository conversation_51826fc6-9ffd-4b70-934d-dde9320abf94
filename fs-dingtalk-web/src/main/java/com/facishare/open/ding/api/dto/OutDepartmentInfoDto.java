package com.facishare.open.ding.api.dto;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.OutDeptData;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 外部部门信息DTO
 */
@Data
@Slf4j
@Accessors(chain = true)
public class OutDepartmentInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 渠道类型
     */
    private ChannelEnum channel;

    /**
     * 外部企业账号
     */
    private String outEa;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 外部部门ID
     */
    private String outDepartmentId;

    /**
     * 部门扩展信息
     */
    private OutDeptData outDeptData;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    public static OutDepartmentInfoDto convertToDto(OuterOaDeptDataEntity deptDataEntity) {
        if (deptDataEntity == null) {
            return null;
        }
        OutDepartmentInfoDto dto = new OutDepartmentInfoDto();
        dto.setId(deptDataEntity.getId());
        dto.setChannel(deptDataEntity.getChannel());
        dto.setOutEa(deptDataEntity.getOutEa());
        dto.setAppId(deptDataEntity.getAppId());
        dto.setOutDepartmentId(deptDataEntity.getOutDeptId());
        try {
            OutDeptData outDeptData = deptDataEntity.getOutDeptInfo().toJavaObject(OutDeptData.class);
            dto.setOutDeptData(outDeptData);
        } catch (Exception e) {
            log.error("convertToDto parse outDeptInfo error", e);
        }
        dto.setCreateTime(deptDataEntity.getCreateTime());
        dto.setUpdateTime(deptDataEntity.getUpdateTime());
        return dto;
    }
} 