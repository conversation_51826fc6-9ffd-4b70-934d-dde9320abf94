package com.facishare.open.ding.web.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/18
 **/
@Slf4j
@Service
public  class HttpManager {
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    @Resource
    @Qualifier("okHttpSupport")
    private OkHttpSupport okHttpSupport;


    private RequestBody createRequestBody(Object params) {
        return params instanceof String ? RequestBody.create(JSON_TYPE, params.toString()) : RequestBody.create(JSON_TYPE, JSONObject.toJSONString(params));
    }

    public <T> T postUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(this.createRequestBody(params)).build();
        T result = null;
        try{
            result = this.okHttpSupport.parseObject(request, typeReference);
        }catch (Exception e){
            log.error("post url failed, url={}, params={}, headerMap={}", url, params, headerMap);
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, params, headerMap, result);
        return result;
    }

    public <T> T getUrl(String url, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        T result = this.okHttpSupport.parseObject(request, typeReference);
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result;
    }

    public Map<String,Object> postUrl(String url, Object params, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        Map<String,Object> resultMap= Maps.newHashMap();
        RequestBody requestBody = this.createRequestBody(params);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = this.okHttpSupport.syncExecute(request, new SyncCallback() {
            public Object response(Response response) throws IOException {
                Headers headers = response.headers();
                Date date = new Date(headers.get("Date"));
                log.info("dateTime:{}",date.getTime());
                log.info("response :{}", response);
                resultMap.put("time",date);
                resultMap.put("body",response.body().string());
                return resultMap;
            }
        });
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, params, headerMap, result);
        return resultMap;
    }

    public String putUrl(String url, Object params, Map<String, String> headerMap) {
        log.debug("putUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).put(this.createRequestBody(params)).build();
        Object result = this.okHttpSupport.syncExecute(request, new SyncCallback() {
            public Object response(Response response) throws IOException {
                return response.body().string();
            }
        });
        log.debug("putUrl result, url={}, params={}, headerMap={}, result={}", url, params, headerMap, result);
        return result.toString();
    }

    public String getUrl(String url, Map<String, String> headerMap) {

        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        Object result = this.okHttpSupport.syncExecute(request, new SyncCallback() {
            public Object response(Response response) throws IOException {
                return response.body().string();
            }
        });
        log.info("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result.toString();
    }


}
