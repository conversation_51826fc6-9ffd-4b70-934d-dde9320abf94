package com.facishare.open.ding.provider.task.executor;

import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class InsertEmployeeTask implements Runnable {
    private Map<String, User> userMap;
    private Integer ei;
    private Long deptId;
    private String deptName;
    private Integer userId;
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    private String appId;

    public InsertEmployeeTask(DingMappingEmployeeManager dingMappingEmployeeManager, Map<String, User> userMap, Integer ei, Integer userId, Long deptId, String deptName, String appId) {
        this.userMap = userMap;
        this.ei = ei;
        this.deptId = deptId;
        this.deptName = deptName;
        this.userId=userId;
        this.dingMappingEmployeeManager=dingMappingEmployeeManager;
        this.appId = appId;
    }

    @Override
    public void run() {
        List<User> differEmployeeList = Lists.newArrayList();
        userMap.entrySet().parallelStream().forEach(item -> differEmployeeList.add(item.getValue()));
        //查询指定的员工 如何快速确定员工是否在数据库中

        //保存钉钉员工信息
        Integer count = dingMappingEmployeeManager.initMappingEmployee(differEmployeeList, ei, deptId, deptName, appId);
        if (Objects.nonNull(count) && count.equals(differEmployeeList.size())) {
            log.info("新增员工成功" + count + "条");
            log.info("insert employee list:{}", differEmployeeList);
        } else {
            log.warn("新增员工失败，deptId={},deptName={}.", deptId, deptName);
        }
    }
}
