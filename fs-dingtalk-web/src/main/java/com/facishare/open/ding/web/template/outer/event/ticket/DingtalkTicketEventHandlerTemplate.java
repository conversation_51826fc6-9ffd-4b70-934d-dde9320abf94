package com.facishare.open.ding.web.template.outer.event.ticket;

import com.facishare.open.ding.web.manager.DingtalkApiManager;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.ticket.TicketEventHandlerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 钉钉ticket事件处理器模板实现类
 * <AUTHOR>
 * @date 2024-09-10
 */

@Slf4j
@Component
public class DingtalkTicketEventHandlerTemplate extends TicketEventHandlerTemplate {
    @Resource
    private DingtalkApiManager dingtalkApiManager;

    @Override
    public void onTicketEvent(MethodContext context) {
        log.info("DingtalkTicketEventHandlerTemplate.onTicketEvent,context={}",context);
        String outEa = context.getData();

        String ticket = dingtalkApiManager.getTicket(outEa);
        log.info("DingtalkTicketEventHandlerTemplate.onTicketEvent,ticket={}",ticket);
        context.setResult(TemplateResult.newSuccess(ticket));
    }
}
