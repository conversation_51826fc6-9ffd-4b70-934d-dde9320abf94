package com.facishare.open.ding.api.enums;

/**
 * <p>CRM对象枚举列表</p>
 * @dateTime 2018/7/13 15:00
 * <AUTHOR> y<PERSON><PERSON>@fxiaoke.com
 * @version 1.0 
 */
@SuppressWarnings("unused")
public enum CrmApiNameEnum {

    AccountObj("AccountObj", "客户"),

    SalesOrderObj("SalesOrderObj", "销售订单"),

    ProductObj("ProductObj", "产品"),

    SPUProductObj("SPUObj", "商品"),

    ContactObj("ContactObj", "联系人"),

    ContractObj("ContractObj", "合同"),

    LeadsObj("LeadsObj", "销售线索"),

    LeadsPoolObj("LeadsPoolObj", "销售线索池"),

    InvoiceApplicationObj("InvoiceApplicationObj", "开票申请"),

    OpportunityObj("OpportunityObj", "商机"),

    MarketingEventObj("MarketingEventObj", "市场活动"),

    PaymentObj("PaymentObj", "回款"),

    OrderPaymentObj("OrderPaymentObj", "回款明细"),

    RefundObj("RefundObj", "退款"),

    SalesOrderProductObj("SalesOrderProductObj", "销售订单产品"),

    VisitingObj("VisitingObj", "拜访"),

    HighSeasObj("HighSeasObj", "公海"),

    ErpWarehouseObj("ErpWarehouseObj", "ERP仓库"),

    WarehouseObj("WarehouseObj", "纷享仓库"),

    ErpStockObj("ErpStockObj", "ERP库存"),

    StockObj("StockObj", "纷享库存"),

    SaleActionObj("SaleActionObj", "销售流程"),

    SaleActionStageObj("SaleActionStageObj", "销售阶段"),

    DeliveryNoteObj("DeliveryNoteObj", "发货单"),

    DeliveryNoteProductObj("DeliveryNoteProductObj", "发货单产品"),

    ReturnedGoodsInvoiceObj("ReturnedGoodsInvoiceObj", "退货单"),

    BatchStockObj("BatchStockObj", "批次库存"),

    BatchObj("BatchObj", "批次"),

    SelectOneFieldObj("SelectOneFieldObj","下拉字段"),

    EmpObj("EmpObj","员工"),

    DepObj("DepObj","部门");

    private String apiName;

    private String description;

    CrmApiNameEnum(String apiName, String description) {
        this.apiName = apiName;
        this.description = description;
    }

    public String getApiName() {
        return apiName;
    }

    public String getDescription() {
        return description;
    }

    public static boolean isInvalid(String apiName) {
        if (apiName != null) {
            for (CrmApiNameEnum crmApiNameEnum : CrmApiNameEnum.values()) {
                if (crmApiNameEnum.getApiName() == apiName) {
                    return false;
                }
            }
        }
        return true;
    }

    public static String getExceptionByName(String apiName) {
        for (CrmApiNameEnum e : CrmApiNameEnum.values()) {
            if (apiName.equals(e.apiName)) {
                return e.description;
            }
        }
        return null;
    }

}
