package com.facishare.open.ding.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/5/11 19:41
 * @Version 1.0
 */
@Getter
public enum DingDeptEventEnum {

    //bizType 14
    ORG_DEPT_CREATE("org_dept_create"),
    ORG_DEPT_MODIFY("org_dept_modify"),
    ORG_DEPT_REMOVE("org_dept_remove"),


    ;

    private  String action;

    DingDeptEventEnum(String action){

        this.action=action;
    }

    public static DingDeptEventEnum getBizTypeAuth(String action){
        for (DingDeptEventEnum e : DingDeptEventEnum.values()){
            if(e.action.equals(action)){
                return e;
            }
        }
        return null;
    }

}
