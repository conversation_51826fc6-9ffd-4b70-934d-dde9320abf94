package com.facishare.open.ding.common.model;

import com.google.common.base.Joiner;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by system on 2018/3/29.
 */
@Data
public class BaseVo implements Serializable {

    /** 当前操作人的员工ID */
    protected Integer userId;

    /** 当前操作人的公司ID，即EI */
    protected Long corpId;

    /** 当前操作人的公司账号，即EA */
    protected String enterpriseAccount;

    public String getFsUserId() {
        return Joiner.on(".").join("E", enterpriseAccount, userId);
    }

}
