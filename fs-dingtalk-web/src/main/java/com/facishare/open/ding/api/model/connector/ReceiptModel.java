package com.facishare.open.ding.api.model.connector;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 连接器收款单对象模型
 * <AUTHOR>
 * @date 2021/07/27
 */
@Data
public class ReceiptModel extends BaseConnectorObjectModel {
    private DataModel data = new DataModel();

    @Data
    public static class DataModel {
        private String amount;//总金额 必填
        private String code;//单据编码 必填
        private String payType;//收款方式
        private String empCode;//业务员编码
        private List<STMTModel> stmtList;//应收款(回款计划)编码列表
        private String empName;//业务员姓名
        private String customerCode;//客户编码 必填
        private Long billDate;//单据日期
        private String remark;//备注
        private String customerName;//客户名称 必填
        private String settleAccount;//收款账户
        private List<OrderSettlementModel> orderSettlementList = new ArrayList<>(); //订单结算清单  必填
    }

    @Data
    public static class STMTModel {
        private String stmtAmount;//应收款总额
        private String stmtPaymentAmount;//应收款收款金额
        private String stmtCode;//应收款编码
    }

    @Data
    public static class OrderSettlementModel {
        private String orderPaymentAmount;//订单收款金额
        private String orderCode;//订单编码
        private String settlementAmount;//总计金额
    }
}
