package com.facishare.open.ding.api.model.connector;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 连接器销售订单对象模型
 * <AUTHOR>
 * @date 2021/07/27
 */
@Data
public class SalesOrderModel extends BaseConnectorObjectModel {
    private DataModel data = new DataModel();
    @Data
    public static class DataModel {
        private String amount; //订单总额
        private String code; //单据编码 必填
        private String customerCode;//客户编码 必填
        private Long billDate;//单据日期 必填
        private String remark;//单据备注
        private String customerName;//客户名称
        private String empCode;//业务员编码 业务必填
        private String empName;//业务员姓名
        private String name;//订单名称
        private Long deadline;//到期日期
        private String signPersonName;//签订人用户姓名
        private String signPersonId;//签订人用户id
        private String status;//单据状态
        private List<ProductModel> productList = new ArrayList<>();//产品列表
    }

    @Data
    public static class ProductModel {
        private String discountRate;//明细折扣率
        private String amount;//价税合计
        private String productCode;//产品编码 必填
        private String quantity;//数量 必填
        private String price;//单价
        private String unitCode;//单位编码 必填
        private String name;//产品名称
        private String taxPrice;//含税单价
        private String remark;//产品备注
        private Long deliveryDate;//预计交货日期  必填
        private String skuCode;//规格编码
        private String warehouseCode;//仓库编码
    }
}
