package com.facishare.open.ding.cloud.task;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.open.ding.api.enums.BizDataTypeEnum;
import com.facishare.open.ding.api.result.PollingSyncResult;
import com.facishare.open.ding.api.service.PollingSyncService;
import com.facishare.open.ding.api.service.SyncBizDataService;
import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.api.vo.PollingSyncDataVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.EventManager;
import com.facishare.open.ding.cloud.utils.TraceUtil;
import com.facishare.open.ding.common.result.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/5/11 16:54 高级别轮询
 * @Version 1.0
 */
@JobHander(value = "DingAutoHighLevelJob")
@Component
@Slf4j
// IgnoreI18nFile
public class DingAutoHighLevelJob extends IJobHandler {

    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private PollingSyncService pollingSyncService;
    @Autowired
    private EventManager eventManager;
    private static ExecutorService executorService = new ThreadPoolExecutor(10, 50, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue(100));


    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            TraceUtil.initTrace(String.format("fs-dingtalk-cloud-%s", UUIDUtil.getUUID()));
            //临时
            if(ConfigCenter.isTemGray) {
                Result<PollingSyncResult> syncResult = pollingSyncService.queryPolling(BizDataTypeEnum.HIGH_LEVEL.getType());

                Long lastSyncTime = syncResult.getData().getLastSyncTime();
                Long nowTime = Instant.now().getEpochSecond() * 1000;
                //由于需要分页操作，把逻辑放在了batchHighQueryData去处理
                Result<List<HighBizDataVo>> syncDataList = syncBizDataService.batchHighQueryDataTem(lastSyncTime,nowTime);
                log.info("high auto job query mapping lasttime:{},nowTime:{},data:{}",lastSyncTime,nowTime,syncDataList);
            } else {
                Result<List<HighBizDataVo>> syncDataList = syncBizDataService.batchHighQueryData();
                log.info("high auto job query mapping,data:{}",syncDataList);
            }


//            Result<PollingSyncResult> syncResult = pollingSyncService.queryPolling(BizDataTypeEnum.HIGH_LEVEL.getType());
//
//            Long lastSyncTime = syncResult.getData().getLastSyncTime();
//            Long nowTime = Instant.now().getEpochSecond() * 1000;
            //由于需要分页操作，把逻辑放在了batchHighQueryData去处理
            //不用时间的原因是还是有可能会漏数据，入库成功和入库的时间会有出入，导致查询失败，采用逻辑删除的形式，钉钉提供了 status
//            Result<List<HighBizDataVo>> syncDataList = syncBizDataService.batchHighQueryData();
//            log.info("high auto job query mapping,data:{}",syncDataList);
//            //按照企业划分赛道
//            if(CollectionUtils.isNotEmpty(syncDataList.getData())){
//                Map<String,List<HighBizDataVo>> groupEnterPrise=Maps.newHashMap();
//                syncDataList.getData().stream().forEach( item ->{
//                    if(ObjectUtils.isEmpty(groupEnterPrise.get(item.getCorpId()))){
//                        groupEnterPrise.put(item.getCorpId(),Lists.newArrayList(item));
//                    }else {
//                        List<HighBizDataVo> dataVos = groupEnterPrise.get(item.getCorpId());
//                        dataVos.add(item);
//                        groupEnterPrise.put(item.getCorpId(),dataVos);
//                    }
//                });
//                //多线程执行
//               groupEnterPrise.keySet().stream().forEach(temp ->{
//                   executorService.execute(new Runnable() {
//                       @Override
//                       public void run() {
//                           TraceUtil.initTrace(String.format("fs-dingtalk-cloud-%s",UUIDUtil.getUUID()));
//                           eventManager.doProcessEvent(groupEnterPrise.get(temp));
//                       }
//                   });
//               });
//            }
            log.info("DingAutoHighLevelJob is complete.");
            TraceUtil.removeTrace();
            return new ReturnT(ReturnT.SUCCESS_CODE, "高级定时任务调用成功");
        } catch (Exception e) {
            log.error("高级定时任务调用异常调用异常，error:", e);
            throw e;
        }

    }
}
