package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.api.result.CrmResponseResult;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.api.utils.HttpRequestUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <p>类的详细说明</p>
 * @<NAME_EMAIL>
 * @version 1.0
 * @dateTime 2018/7/13 14:50
 */
@Slf4j
public class CrmRequestUtils {

    private static final String SOURCE_HEADER = "OpenAPI-V2.0";

    private static final JsonParser jsonParser = new JsonParser();

    private static final Gson gson = new Gson();

    /**
     * POST查询接口,幂等操作支持2次额外重试：包括获取客户列表， 获取客户订单列表，获取产品单位或分类列表
     * PS：只能处理产品一级分类
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @param target
     * @param <T>
     * @return
     */
    public static <T> CrmResponseResult<T> queryAndRetry(String url, int ei, int userId, String json, Class<T> target) {
        CrmResponseResult<T> result = query(url, ei, userId, json, target);
        int retryTimes = 2;
        for (int index = 0; index < retryTimes; index++) {
            if (result.isNeedRetry()) {
                result = query(url, ei, userId, json, target);
                continue;
            }
            break;
        }
        return result;
    }

    /**
     * GET查询接口, 幂等操作支持2次额外重试：根据ID获取数据详情
     * @param url
     * @param ei
     * @param userId
     * @param params
     * @param target
     * @param <T>
     * @return
     */
    public static <T> CrmResponseResult<T> getAndRetry(String url, int ei, int userId, Map<String, Object> params, Class<T> target) {
        CrmResponseResult<T> result = get(url, ei, userId, params, target);
        int retryTimes = 2;
        for (int index = 0; index < retryTimes; index++) {
            if (result.isNeedRetry()) {
                result = get(url, ei, userId, params, target);
                continue;
            }
            break;
        }
        return result;
    }

    /**
     * 创建操作: 创建商品
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<String> createAndGetId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            // 处理解析逻辑是否一致
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = jsonObject.get("errMessage").getAsString();
            } else {
                errorMessage = jsonObject.get("errMessage").getAsString();
                JsonObject resultData = jsonObject.get("result").getAsJsonObject();
                JsonObject jsonData = resultData.get("objectData").getAsJsonObject();
                String objectDataId = jsonData.get("_id").getAsString();
                responseResult.setData(objectDataId);
            }
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("createAndGetId error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 创建客户，并且拿到创建客户的_id
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<String> createAccountAndGetId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);
        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("code").getAsInt();
            String errorMessage = jsonObject.get("message").getAsString();

            if (errorCode == HttpRequestUtils.CRM_SUCCESS) {
                JsonObject result = jsonObject.get("data").getAsJsonObject();
                JsonObject objectDate=result.get("objectData").getAsJsonObject();
                responseResult.setData(objectDate.get("_id").getAsString());
            }
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("createAndGetId error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }
    /**
     * 创建erp或v2对象操作: 包括创建库存和回款
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<String> createErpOrV2AndGetId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("createErpOrV2AndGetId sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }

            // 处理解析逻辑目前只处理回款返回
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage = jsonObject.get("errMessage").getAsString();

            if (errorCode == 0) {
                JsonObject result = jsonObject.get("result").getAsJsonObject();
                JsonObject jsonData = result.get("objectData").getAsJsonObject();
                String objectDataId = jsonData.get("_id").getAsString();
                responseResult.setData(objectDataId);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("createErpOrV2AndGetId error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 创建erp或v2对象操作: 包括创建库存和回款
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<String> createErpAndGetId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("createErpOrV2AndGetId sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            // 处理解析逻辑是否一致
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();

            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = jsonObject.get("errMessage").getAsString();
            } else {
                errorMessage = jsonObject.get("errMessage").getAsString();
                JsonObject result = jsonObject.get("result").getAsJsonObject();
                JsonObject jsonData = result.get("objectData").getAsJsonObject();
                String objectDataId = jsonData.get("_id").getAsString();
                responseResult.setData(objectDataId);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("createErpOrV2AndGetId error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }


    /**
     * 更新操作: 确认增量更新，更新产品
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<Void> update(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage = jsonObject.get("errMessage").getAsString();
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        } catch (Exception e) {
            log.error("update error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }


    /**
     * 更新操作: 确认增量更新，更新产品
     * @return
     */
    public static CrmResponseResult<JsonObject> getDetail(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<JsonObject> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage = jsonObject.get("errMessage").getAsString();
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            if (HttpRequestUtils.CRM_SUCCESS == errorCode) {
                responseResult.setData(jsonObject.getAsJsonObject("result").getAsJsonObject("data"));
            }
            return responseResult;
        } catch (Exception e) {
            log.error("update error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }


    /**
     * 更新操作: 确认增量更新，更新产品
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<Void> updatePut(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Put(url, headers, json);
            log.info("sendOkHttp3Put url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonObject.getAsJsonObject("result");
            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (HttpRequestUtils.CRM_SUCCESS != errorCode) {
                errorMessage = result.get("errorDetail").getAsString();
            } else {
                errorMessage = result.get("message").getAsString();
            }
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        } catch (Exception e) {
            log.error("update error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 更新操作 v2 rest
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<Void> updatePost(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
//            JsonObject result = jsonObject.getAsJsonObject("result");
            int errorCode = jsonObject.get("code").getAsInt();
            String errorMessage = jsonObject.get("message").getAsString();
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        } catch (Exception e) {
            log.error("update error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 更新操作: 更新库存
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<Void> updateERPStock(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Put url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }

            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage = jsonObject.get("errMessage").getAsString();

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("update error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<Void> postEdit(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("postEdit sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }

            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();

            int errorCode = jsonObject.get("errCode").getAsInt();
            String errorMessage = jsonObject.get("errMessage").getAsString();
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("postEdit error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 废弃销售订单操作,暂不需要  V1
     * @param url
     * @param ei
     * @param userId
     * @return
     */
    public static CrmResponseResult<Void> invalidObjectData(String url, int ei, int userId) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Delete(url, headers);
            log.info("sendOkHttp3Delete url=[{}], headers[{}], result=[{}].", url, headers, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonObject.get("result").getAsJsonObject();

            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = result.get("errorDetail").getAsString();
            } else {
                errorMessage = result.get("message").getAsString();
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("invalidObjectData error, url={} ei={} userId={}", url, ei, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 作废接口 V2 rest
     * @param url
     * @param ei
     * @param userId
     * @return
     */
    public static CrmResponseResult<Void> invalidv2ObjectData(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Delete url=[{}], headers[{}], result=[{}].", url, headers, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
//            JsonObject result = jsonObject.get("data").getAsJsonObject();

            int errorCode = jsonObject.get("code").getAsInt();
            String errorMessage = "";
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = jsonObject.get("message").getAsString();
            }
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("invalidObjectData error, url={} ei={} userId={}", url, ei, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    public static CrmResponseResult<String> createCategoryAndGetId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }

            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("code").getAsInt();
            String errorMessage = jsonObject.get("message").getAsString();
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                return responseResult;
            }
            JsonObject result = jsonObject.get("data").getAsJsonObject().get("result").getAsJsonObject();

            String categoryCode = result.get("code").getAsString();
            responseResult.setData(categoryCode);

            return responseResult;
        } catch (Exception e) {
            log.error("create error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 支持创建产品分类或产品单位，以及产品上下架
     * @param url
     * @param ei
     * @param userId
     * @param json
     * @return
     */
    public static CrmResponseResult<Void> create(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<Void> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }

            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonObject.get("result").getAsJsonObject();

            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = result.get("errorDetail").getAsString();
            } else {
                errorMessage = result.get("message").getAsString();
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("create error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    // HTTP POST查询Erp对象接口
    public static <T> CrmResponseResult<T> queryErp(String url, int ei, int userId, String json, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonContent.get("result").getAsJsonObject();

            int errorCode = jsonContent.get("errCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = jsonContent.get("errMessage").getAsString();
            } else {
                errorMessage = jsonContent.get("errMessage").getAsString();
                T data = gson.fromJson(result, target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     *V2查询列表（rest）
     */
    public static <T> CrmResponseResult<T> queryRest(String url, int ei, int userId, String json, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonContent.get("data").getAsJsonObject();

            int errorCode = jsonContent.get("code").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = jsonContent.get("message").getAsString();
            } else {
                errorMessage = jsonContent.get("message").getAsString();
                T data = gson.fromJson(result, target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     *V2根据Id查询详情（rest）
     */
    public static <T> CrmResponseResult<T> getByIdRest(String url, int ei, int userId, String json, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();

            int errorCode = jsonContent.get("code").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = jsonContent.get("message").getAsString();
            } else {
                JsonObject result = jsonContent.get("data").getAsJsonObject().get("data").getAsJsonObject();
                errorMessage = jsonContent.get("message").getAsString();
                T data = gson.fromJson(result, target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }


    // HTTP POST查询Erp库存对象接口，为解析库存返回数据
    public static <T> CrmResponseResult<T> queryErpStockDetail(String url, int ei, int userId, String json, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonContent.get("errCode").getAsInt();
            String errorMessage = jsonContent.get("errMessage").getAsString();
            if (errorCode == HttpRequestUtils.CRM_SUCCESS) {
                JsonObject result = jsonContent.get("result").getAsJsonObject();
                T data = gson.fromJson(result.get("data"), target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    // HTTP POST查询接口
    private static <T> CrmResponseResult<T> query(String url, int ei, int userId, String json, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonContent.get("result").getAsJsonObject();

            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = result.get("errorDetail").getAsString();
            } else {
                errorMessage = result.get("message").getAsString();

                T data = gson.fromJson(result.get("queryResult"), target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    // HTTP GET查询接口
    private static <T> CrmResponseResult<T> get(String url, int ei, int userId, Map<String, Object> params, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Get(url, headers, params);
            log.info("sendOkHttp3Get url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, params, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonContent.get("result").getAsJsonObject();
            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = result.get("errorDetail").getAsString();
            } else {
                errorMessage = result.get("message").getAsString();

                T data = gson.fromJson(result.get("object_data"), target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} ", url, ei, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 构建SFA接口http header
     * @param ei
     * @param userId
     * @return
     */
    private static Map<String, String> buildCrmRequestHeaders(int ei, int userId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-type", "application/json;charset=utf-8");
        headers.put("x-fs-peer-name", SOURCE_HEADER);
        headers.put("x-fs-ei", "" + ei);
        headers.put("x-fs-userInfo", "" + userId);

        return headers;
    }

    // HTTP GET查询接口查询对象描述
    public static <T> CrmResponseResult<T> getDescribe(String url, int ei, int userId, Map<String, Object> params, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Get(url, headers, params);
            log.info("sendOkHttp3Get url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, params, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());

                return responseResult;
            }

            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonContent.get("result").getAsJsonObject();
            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (errorCode != HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = result.get("errorDetail").getAsString();
            } else {
                errorMessage = result.get("message").getAsString();

                T data = gson.fromJson(result.get("describe"), target);
                responseResult.setData(data);
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        }catch (JsonSyntaxException jsonExe){
            log.warn("json 解析异常 该企业ei[{}] 没有该对象的描述信息" , ei);
            return HttpRequestUtils.buildFailureCrmResponseResult(jsonExe);
        } catch (Exception e) {
            log.error("query error, url={} ei={} ", url, ei, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 获取paas对象描述
     */
    public static <T> CrmResponseResult<T> getPaasDescribe(String url, int ei, int userId, Map<String, Object> params, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);

        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Get(url, headers, params);
            log.info("sendOkHttp3Get url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, params, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonContent.get("code").getAsInt();
            String errorMessage = jsonContent.get("message").getAsString();
            if (errorCode == HttpRequestUtils.CRM_SUCCESS) {
                JsonObject result = jsonContent.get("data").getAsJsonObject();
                T data = gson.fromJson(result.get("describe"), target);
                responseResult.setData(data);
            }
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} ", url, ei, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 更新paas对象描述
     */
    public static <T> CrmResponseResult<T> updatePaasDescribe(String url, int ei, int userId, String params, Class<T> target) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);
        CrmResponseResult<T> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, params);
            log.info("sendOkHttp3Get url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, params, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonContent = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonContent.get("code").getAsInt();
            String errorMessage = jsonContent.get("message").getAsString();
            if (errorCode == HttpRequestUtils.CRM_SUCCESS) {
                JsonObject result = jsonContent.get("data").getAsJsonObject();
                T data = gson.fromJson(result.get("describe"), target);
                responseResult.setData(data);
            }
            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);
            return responseResult;
        } catch (Exception e) {
            log.error("query error, url={} ei={} ", url, ei, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }


    /**
     * 创建发票，返回id
     */
    public static CrmResponseResult<String> createAndReturnId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);
        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            JsonObject result = jsonObject.get("result").getAsJsonObject();
            int errorCode = result.get("errorCode").getAsInt();
            String errorMessage;
            if (errorCode == HttpRequestUtils.CRM_SUCCESS) {
                errorMessage = result.get("message").getAsString();
                responseResult.setData(result.getAsJsonObject("object_data").get("_id").getAsString());
            } else {
                errorMessage = result.get("errorDetail").getAsString();
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("create error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

    /**
     * 创建对象，返回id  V2-rest
     */
    public static CrmResponseResult<String> createV2AndReturnId(String url, int ei, int userId, String json) {
        Map<String, String> headers = buildCrmRequestHeaders(ei, userId);
        CrmResponseResult<String> responseResult = new CrmResponseResult<>();
        try {
            HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, headers, json);
            log.info("sendOkHttp3Post url=[{}], headers[{}], params=[{}], result=[{}].", url, headers, json, response);
            String content = response.getContent();
            if (Strings.isNullOrEmpty(content)) {
                responseResult.setErrorCode(-response.getStatusCode());
                responseResult.setErrorMessage(response.getMessage());
                return responseResult;
            }
            JsonObject jsonObject = jsonParser.parse(content).getAsJsonObject();
            int errorCode = jsonObject.get("code").getAsInt();
            String errorMessage = jsonObject.get("message").getAsString();

            if (errorCode == HttpRequestUtils.CRM_SUCCESS) {
                JsonObject result = jsonObject.get("data").getAsJsonObject();
                responseResult.setData(result.get("objectData").getAsJsonObject().get("_id").getAsString());
            }

            responseResult.setErrorCode(errorCode);
            responseResult.setErrorMessage(errorMessage);

            return responseResult;
        } catch (Exception e) {
            log.error("create error, url={} ei={} json={}", url, ei, json, e);
            return HttpRequestUtils.buildFailureCrmResponseResult(e);
        }
    }

}
