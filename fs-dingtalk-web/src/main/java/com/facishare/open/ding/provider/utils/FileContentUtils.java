package com.facishare.open.ding.provider.utils;

import java.io.*;

/**
 * <AUTHOR>
 * @Date 2020/11/24 16:11
 * @Version 1.0
 */
// IgnoreI18nFile
public class FileContentUtils {

        /**
         * 追加文件：使用FileOutputStream，在构造FileOutputStream时，把第二个参数设为true
         *
         */
        public static void method1(String file, String conent) {
            BufferedWriter out = null;
            try {
                out = new BufferedWriter(new OutputStreamWriter(
                        new FileOutputStream(file, true)));
                out.write(conent);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        /**
         * 追加文件：使用FileWriter
         *
         * @param fileName
         * @param content
         */
        public static void method2(String fileName, String content) {
            try {
                // 打开一个写文件器，构造函数中的第二个参数true表示以追加形式写文件
                FileWriter writer = new FileWriter(fileName, true);
                writer.write(content);
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        /**
         * 追加文件：使用RandomAccessFile
         *
         * @param fileName
         *            文件名
         * @param content
         *            追加的内容
         */
        public static void method3(String fileName, String content) {
            try {
                // 打开一个随机访问文件流，按读写方式
                RandomAccessFile randomFile = new RandomAccessFile(fileName, "rw");
                // 文件长度，字节数
                long fileLength = randomFile.length();
                // 将写文件指针移到文件尾。
                randomFile.seek(fileLength);
                randomFile.writeBytes(content);
                randomFile.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        public static void main(String[] args) {
            System.out.println("start");
            method1("D:/test.txt", "追加到文件的末尾");
            System.out.println("end");
        }
    }
