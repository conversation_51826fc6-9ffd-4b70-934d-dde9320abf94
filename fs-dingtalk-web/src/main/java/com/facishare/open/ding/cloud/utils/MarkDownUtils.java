package com.facishare.open.ding.cloud.utils;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/6/3 15:16
 * @Version 1.0
 */
@Data
public class MarkDownUtils  {

    public static String convertMarkDown(Map<String,String> argMap){
        Boolean first=true;
        StringBuilder contentBuilder=new StringBuilder();
        for (String item : argMap.keySet().stream().collect(Collectors.toList())) {
            if(first){
                contentBuilder.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"+item+":"+argMap.get(item)+"</font>\n"+  "        <br>\n" );
                first=false;
            }else {
                contentBuilder.append("<font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">"+item+"：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">"+argMap.get(item)+"</font>");
            }
        }
        return contentBuilder.toString();
    }
}
