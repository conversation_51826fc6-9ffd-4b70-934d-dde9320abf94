package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.vo.CropMappingVo;

import java.util.List;
import java.util.Map;

public interface DssSyncDataMappingService {

    /*迁移数据映射表。从集成平台映射表（pg）迁移到钉钉的库表(mysql)*/
    void dataMappingMove(List<CropMappingVo> cropMappingVos);

    /*迁移数据映射表。从集成平台映射表（pg）迁移到钉钉的库表(mysql)*/
    Map<Integer,String> getMappginCount(List<CropMappingVo> cropMappingVos);



}
