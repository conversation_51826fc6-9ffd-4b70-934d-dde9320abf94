package com.facishare.open.ding.provider.model.result.kis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * KIS客户信息
 * Created by system on 2018/5/18.
 */
@Data
public class Customer {

    /** KIS客户内码ID **/
    @SerializedName(value = "ItemID")
    private Integer itemId;

    /** 客户编码 **/
    @SerializedName(value = "Number")
    private String number;

    /** 客户名称 **/
    @SerializedName(value = "Name")
    private String name;

    /** 业务负责人ID（对应职员ItemID），未设置则返回0 **/
    @SerializedName(value = "EmpID")
    private Integer ownerId;

    /** 客户状态：0使用中 1禁用 **/
    @SerializedName(value = "Status")
    private Integer status;

}
