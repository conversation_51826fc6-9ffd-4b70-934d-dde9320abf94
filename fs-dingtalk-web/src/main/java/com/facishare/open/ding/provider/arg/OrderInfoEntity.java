package com.facishare.open.ding.provider.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.Alias;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "order_info")
public class OrderInfoEntity implements Serializable {
    private Long id;

    private String corpId;

    private String mainCorpId;

    private String ea;

    private Long orderId;

    private String orderType;

    private String openId;

    private String unionId;

    private Integer subQuantity;

    private Integer suiteId;

    private String suiteKey;

    private String goodName;

    private String orderChargeType;//试用订单是TRYOUT

    private Integer minOfPeople;

    private Integer maxOfPeople;


    private String goodsCode;


    private String itemName;

    private String itemCode;

    private Date paidTime;

    private Date serviceStartTime;

    private Date serviceStopTime;

    private Long payFee;

    private Long nominalPayFee;

    private Long discountFee;

    private Double discount;

    private String distributorCorpId;

    private String distributorCorpName;
}