package com.facishare.open.ding.provider.service.cloud;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONPath;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.enums.EmpStatusEnum;
import com.facishare.open.ding.api.enums.OperationStatusEnum;
import com.facishare.open.ding.api.enums.OperationTypeEnum;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.UserAppMappingService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.vo.CreateCrmEmployeeVo;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.entity.LogWriteVo;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.ding.provider.service.DingDeptService;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.RemoveEmployeeEventType;
import com.facishare.open.outer.oa.connector.common.api.info.FsEmployeeDetailInfo;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/8 20:06
 * @Version 1.0
 */
@Service("cloudEmpServiceImpl")
@Slf4j
public class CloudEmpServiceImpl implements CloudEmpService {

    @Autowired
    private DingDeptService dingDeptService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private CrmRestManager crmRestManager;
    @Autowired
    private UserAppMappingService userAppMappingService;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    private static final Integer OPERATOR = -10000;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Override
    public Result<Integer> cloudCreateEmp(EmployeeDingVo employeeVo, Integer ei, Long appId, String dingCorpId) {
        StopWatch stopWatch = StopWatch.create("cloud batchCreateEmp");

        //获取数据库里的部门绑定信息
        Set<Long> dingDeptIds = new HashSet<>(employeeVo.getDepartment());
        dingDeptIds.add(employeeVo.getMainDept());
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String appKey = String.valueOf(appId);
        final OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appKey);

        // 校验是否已存在对应的员工映射,同一个企业的员工映射只能有一个
        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(dingCorpId)
                        .fsEmpId(fsEa).outEmpId(employeeVo.getUserid()).channel(ChannelEnum.dingding).build();
        List<OuterOaEmployeeBindEntity> userNotDependAppid = outerOaEmployeeBindManager.getUserNotDependAppid(outerOaEmployeeBindParams);
//        final String fsEmpId = outerOaEmployeeBindManager.getFsEmpIdByEaAndOutEmpId(ChannelEnum.dingding, appKey, enterpriseBindEntity.getFsEa(), enterpriseBindEntity.getOutEa(), employeeVo.getUserid());
        if (CollectionUtils.isNotEmpty(userNotDependAppid)) {
            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity=userNotDependAppid.get(0);
            // 写入绑定表
            outerOaEmployeeBindManager.upsert(new OuterOaEmployeeBindEntity(IdGenerator.get(), ChannelEnum.dingding, enterpriseBindEntity.getId(), enterpriseBindEntity.getFsEa(), enterpriseBindEntity.getOutEa(), appKey, outerOaEmployeeBindEntity.getFsEmpId(), employeeVo.getUserid(), BindStatusEnum.normal, System.currentTimeMillis(), System.currentTimeMillis()));
            return Result.newSuccess(Integer.valueOf(outerOaEmployeeBindEntity.getFsEmpId()));
        }

        List<DeptVo> deptVos = dingDeptService.batchGetDeptVoByDingDeptIds(fsEa, appKey, dingDeptIds.stream().map(String::valueOf).collect(Collectors.toList()));

        //判断部门是否已停用
        DeptVo deptVo = null;
        String mainDept = "999998";
        List<String> viceDeptList = new LinkedList<>();
        for(DeptVo vo : deptVos) {
            Result<String> crmDeptResult = crmRestManager.queryCrmObj(ei,"DepartmentObj", "_id", vo.getCrmDeptId().toString());
            if(crmDeptResult.isSuccess()
                    && StringUtils.isNotEmpty(crmDeptResult.getData())) {
                JSONArray dataList = (JSONArray) JSONPath.read(crmDeptResult.getData(), "$.data.dataList");
                if(dataList.size()!=0
                        && ObjectUtils.isNotEmpty(JSONPath.read(crmDeptResult.getData(), "$.data.dataList[0].status"))
                        && StringUtils.equalsIgnoreCase("0", JSONPath.read(crmDeptResult.getData(), "$.data.dataList[0].status").toString())){
                    if(vo.getDingDeptId().equals(employeeVo.getMainDept())) {
                        mainDept = vo.getCrmDeptId().toString();
                        deptVo = vo;
                    }
                    viceDeptList.add(vo.getCrmDeptId().toString());
                }
            }
        }
        stopWatch.lap("queryCrmDept");

        //查询负责人是否被停用，主属部门被停用或者负责人不存在，不用查询
        Integer deptOwner = null;
        if(ObjectUtils.isNotEmpty(deptVo) && ObjectUtils.isNotEmpty(deptVo.getCrmDeptOwner())) {
            Result<String> crmEmpResult = crmRestManager.queryCrmObj(ei,"PersonnelObj", "_id", deptVo.getCrmDeptOwner().toString());
            if(crmEmpResult.isSuccess()
                    && StringUtils.isNotEmpty(crmEmpResult.getData())) {
                JSONArray dataList = (JSONArray) JSONPath.read(crmEmpResult.getData(), "$.data.dataList");
                if(dataList.size()!=0
                        && ObjectUtils.isNotEmpty(JSONPath.read(crmEmpResult.getData(), "$.data.dataList[0].status"))
                        && StringUtils.equalsIgnoreCase("0", JSONPath.read(crmEmpResult.getData(), "$.data.dataList[0].status").toString())){
                    deptOwner = deptVo.getCrmDeptOwner();
                }
            }
        } else {
            deptVo = DeptVo.builder().dingDeptId(employeeVo.getMainDept()).build();
        }

        stopWatch.lap("queryCrmEmployee");

        // 初始化员工信息,创建fs员工的时候用到
        // 创建员工数据
        final DingTalkEmployeeObject employeeData = initEmployeeData(employeeVo, enterpriseBindEntity);

        String name = employeeData.getName();

        com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> createResult = objectDataManager.createEmployee(enterpriseBindEntity, employeeVo.getUserid());
        //员工配额已满
        if (createResult.getCode() == 44) {
            log.info("create emp account full  :{},ei:{},message:{}", createResult, ei,createResult.getMsg());
            return Result.newError(ResultCode.ENTERPRISE_COUNT_FULL);
        }
        final ObjectData objectData = createResult.getData().getObjectData();
        Integer empId = Objects.isNull(objectData) ? null : objectData.getInt("user_id");
        //钉钉第三方没有手机号，暂时先不考虑手机号相同情况
        if (createResult.getCode() == ResultCode.EMPLOYEE_NAME_IS_EXIST.getErrorCode()) {
            //同名处理，可能是同个员工在多个部门
            Result<DingMappingEmployeeResult> mappingResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, appKey, employeeVo.getUserid());
            if (ObjectUtils.isEmpty(mappingResult.getData())) {
                name = randomName(name);
                employeeData.setName(name);
                outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, enterpriseBindEntity.getId());

                createResult = objectDataManager.createEmployee(enterpriseBindEntity, employeeVo.getUserid());
                if (createResult.isSuccess()) empId = createResult.getData().getObjectData().getInt("user_id");
            } else {
                //已经创建了
                Object empResult = crmRestManager.queryByField(ei, "name", name).getData().get("user_id");
                empId = Integer.parseInt(empResult.toString());
            }
        }

        log.info("create emp result:{}", createResult);
        //加入日志
        Integer statusCode = createResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), name, statusCode, createResult.getMsg());

        if (ObjectUtils.isEmpty(empId))
            return Result.newError(createResult.getCode(), createResult.getMsg());
        stopWatch.lap("createEmployee");
        //插入数据库 DingMappingEmployeeResult
        return Result.newSuccess(empId);
    }

    @NotNull
    private DingTalkEmployeeObject initEmployeeData(EmployeeDingVo employeeVo, OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        String name = employeeVo.getName();//employeeValidName(employeeVo.getName());
        DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
        employeeData.setUserid(employeeVo.getUserid());
        employeeData.setName(name);
        employeeData.setPhone(employeeVo.getMobile());
        employeeData.setUnionId(employeeVo.getUnionid());
        employeeData.setDeptId(employeeVo.getMainDept());
        employeeData.setPosition(employeeVo.getTitle());
        employeeData.setEmail(employeeVo.getEmail());
        outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, enterpriseBindEntity.getId());
        return employeeData;
    }

    @Override
    public Result<Integer> cloudUpdateEmp(EmployeeDingVo employeeDingVo, Integer ei, Long appId, String dingCorpId) {
        //以前的逻辑有的是主属部门为空，部门列表不为空
        if(ObjectUtils.isEmpty(employeeDingVo.getMainDept())) {
            employeeDingVo.setMainDept(employeeDingVo.getDepartment().get(0));
        }

        Result<DingMappingEmployeeResult> mappingResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, employeeDingVo.getUserid(), String.valueOf(appId));
        if (ObjectUtils.isEmpty(mappingResult.getData())) {
            //新增员工的逻辑
            return cloudCreateEmp(employeeDingVo, ei, appId, dingCorpId);
        }
        log.info("batch updateEmp result:{}", mappingResult);

        Integer fxEmpId = mappingResult.getData().getEmployeeId();

        //获取数据库里的部门绑定信息
        Set<Long> dingDeptIds = new HashSet<>(employeeDingVo.getDepartment());
        dingDeptIds.add(employeeDingVo.getMainDept());
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, String.valueOf(appId));

        List<DeptVo> deptVos = dingDeptService.batchGetDeptVoByDingDeptIds(fsEa, appId.toString(), dingDeptIds.stream().map(String::valueOf).collect(Collectors.toList()));

        //判断部门是否已停用
        DeptVo deptVo = null;
        //部门在crm被停用，可能被手动移到别的部门了，这里就不移到待分配了
        String mainDept = null;
        List<String> viceDeptList = new LinkedList<>();
        for(DeptVo vo : deptVos) {
            Result<String> crmDeptResult = crmRestManager.queryCrmObj(ei,"DepartmentObj", "_id", vo.getCrmDeptId().toString());
            if(crmDeptResult.isSuccess()
                    && StringUtils.isNotEmpty(crmDeptResult.getData())) {
                JSONArray dataList = (JSONArray) JSONPath.read(crmDeptResult.getData(), "$.data.dataList");
                if(dataList.size()!=0
                        && ObjectUtils.isNotEmpty(JSONPath.read(crmDeptResult.getData(), "$.data.dataList[0].status"))
                        && StringUtils.equalsIgnoreCase("0", JSONPath.read(crmDeptResult.getData(), "$.data.dataList[0].status").toString())){
                    if(vo.getDingDeptId().equals(employeeDingVo.getMainDept())) {
                        mainDept = vo.getCrmDeptId().toString();
                        deptVo = vo;
                    }
                    viceDeptList.add(vo.getCrmDeptId().toString());
                }
            }
        }

        //查询负责人是否被停用，主属部门被停用或者负责人不存在，不用查询
        //增加这个逻辑是因为主属部门都换了，负责人也需要更换
        //如果一开始有负责人，新的主属部门没有负责人，不会设为空，保留原有的负责人
        Integer deptOwner = null;
        if(ObjectUtils.isNotEmpty(deptVo) && ObjectUtils.isNotEmpty(deptVo.getCrmDeptOwner())) {
            Result<String> crmEmpResult = crmRestManager.queryCrmObj(ei,"PersonnelObj", "_id", deptVo.getCrmDeptOwner().toString());
            if(crmEmpResult.isSuccess()
                    && StringUtils.isNotEmpty(crmEmpResult.getData())) {
                JSONArray dataList = (JSONArray) JSONPath.read(crmEmpResult.getData(), "$.data.dataList");
                if(dataList.size()!=0
                        && ObjectUtils.isNotEmpty(JSONPath.read(crmEmpResult.getData(), "$.data.dataList[0].status"))
                        && StringUtils.equalsIgnoreCase("0", JSONPath.read(crmEmpResult.getData(), "$.data.dataList[0].status").toString())){
                    deptOwner = deptVo.getCrmDeptOwner();
                }
            }
        }

        log.info("modify fxemp viceDeptList:{},ei:{},userid:{},deptVo:{}", viceDeptList, ei, employeeDingVo, deptVo);

        final DingTalkEmployeeObject employeeData = initEmployeeData(employeeDingVo, enterpriseBindEntity);

        com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> modifyResult = objectDataManager.updateEmpData(enterpriseBindEntity, employeeData.getUserid());
        if (modifyResult.getCode() == ResultCode.EMPLOYEE_NAME_IS_EXIST.getErrorCode()) {
            //手机号不同的情况，用手机号后4位 Math.round((Math.random()+1) * 1000)
            StringBuilder nameBuilder = new StringBuilder(employeeData.getName());
            nameBuilder.append("__").append(Math.round((Math.random() + 1) * 1000));
            employeeData.setName(nameBuilder.toString());
            outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, enterpriseBindEntity.getId());
            modifyResult = objectDataManager.updateEmpData(enterpriseBindEntity, employeeData.getUserid());
        }

        Integer statusCode = modifyResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), employeeData.getName(), statusCode, modifyResult.getMsg());
//            logManager.writeLog(logWriteVo, "EmpObj");
        //处于多部门，以前的逻辑是保存多条绑定记录
        Map<Long, DeptVo> deptMap = deptVos.stream()
                .collect(Collectors.toMap(DeptVo::getDingDeptId,
                        Function.identity(),(v1,v2) -> v1));
        updateDingMapping(ei, employeeDingVo.getUserid(), employeeDingVo, deptMap, fxEmpId, String.valueOf(appId));

        return Result.newSuccess();
    }

    private void updateDingMapping(Integer ei, String dingUserId, EmployeeDingVo user, Map<Long, DeptVo> deptMap, Integer fxEmpId, String appId) {
        //根据dingEmpID查询数据，如果不在user.getDept中的数据删除，没有的插入，有的更新
        List<DingMappingEmployeeResult> mappingData = dingMappingEmployeeManager.findDingEmpIdList(ei, dingUserId, appId);
        Map<Long, DingMappingEmployeeResult> maps = mappingData.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingDeptId, Function.identity(), (key1, key2) -> key2));

        user.getDepartment().stream().forEach(itemUpdate -> {
            DeptVo deptVo = Optional.ofNullable(deptMap.get(itemUpdate)).orElseGet(() -> new DeptVo());
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setEi(ei);
            dingMappingEmployee.setDingEmployeeId(dingUserId);
            dingMappingEmployee.setDingUnionId(user.getUnionid());
            dingMappingEmployee.setDingEmployeeName(user.getName());
//            dingMappingEmployee.setEmployeeName(employeeValidName(user.getName()));
            dingMappingEmployee.setEmployeeName(user.getName());
            dingMappingEmployee.setEmployeeId(fxEmpId);
            dingMappingEmployee.setEmployeePhone(user.getMobile());
            dingMappingEmployee.setDingEmployeePhone(user.getMobile());
            dingMappingEmployee.setDingEmployeeStatus(1);
            dingMappingEmployee.setEmployeeStatus(1);
            dingMappingEmployee.setDingDeptId(itemUpdate);
            dingMappingEmployee.setCreateBy(OPERATOR);
            dingMappingEmployee.setUpdateBy(OPERATOR);
            dingMappingEmployee.setCrmDeptId(deptVo.getCrmDeptId());
            dingMappingEmployee.setBindStatus(EmpStatusEnum.BIND.getStatus());
            dingMappingEmployee.setDingDeptName(deptVo.getName());
            if (ObjectUtils.isNotEmpty(maps.get(itemUpdate))) {
                //更新
                dingMappingEmployeeManager.updateModelEmp(dingMappingEmployee, appId);
                log.info("objectMapping update dingMapping service ei:{},arg:{}", ei, dingMappingEmployee);
                maps.remove(itemUpdate);
            } else {
                //插入
                dingMappingEmployeeManager.insertAllModelData(Lists.newArrayList(dingMappingEmployee), ei, appId);
                log.info("objectMapping insert dingMapping service ei:{},arg:{}", ei, dingMappingEmployee);
                maps.remove(itemUpdate);
            }
        });
        if (CollectionUtils.isNotEmpty(maps.keySet())) {
            //删除中间表的数据
            maps.keySet().stream().forEach(itemDelete -> {
                log.info("objectMapping delete dingMapping service ei:{},userid:{},arg:{}", ei, dingUserId, itemDelete);
                dingMappingEmployeeManager.deleteByDeptId(ei, dingUserId, itemDelete, appId);
            });
        }
    }


    @Override
    public Result<Integer> batchRemoveEmp(List<String> dingEmpIds, Integer ei, String dingCorpId, Long appId) {
        for (String dingEmpId : dingEmpIds) {
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setEi(ei);
            dingMappingEmployee.setDingEmployeeId(dingEmpId);
            //停用纷享员工
            DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee, String.valueOf(appId));
            if (Objects.nonNull(mappingEmp) && Objects.nonNull(mappingEmp.getEmployeeId())) {
                final String ea = eieaConverter.enterpriseIdToAccount(ei);
                final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, String.valueOf(appId));
                objectDataManager.removeEmpData(entity, dingEmpId, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
            }
        }


        return Result.newSuccess();
    }

    @Override
    public Result<Integer> batchForbidEmp(String dingEmpId, Integer ei, String dingCorpId, Long appId) {
        Integer count = 0;
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        OuterOaEmployeeBindEntity empAuth = outerOaEmployeeBindManager.getNormalEmployeeBindEntity(ChannelEnum.dingding, dingCorpId, ea, String.valueOf(appId), dingEmpId);
        if (Objects.nonNull(empAuth)) {
            //停用纷享员工
            final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, String.valueOf(appId));
            final com.facishare.open.outer.oa.connector.common.api.result.Result<Void> stopResult = objectDataManager.removeEmpData(entity, dingEmpId, RemoveEmployeeEventType.REMOVE_RANGE);
            if (stopResult.isSuccess()) {
                count++;
            }
        }
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> insertEmpData(List<DingMappingEmployeeResult> employeeResults, String appId) {
        log.info("insertEmpData,employeeResults={}",employeeResults);
        if (CollectionUtils.isEmpty(employeeResults)) return Result.newSuccess();
        Integer allCount = 0;
        for (DingMappingEmployeeResult employeeResult : employeeResults) {
            log.info("insertEmpData,employeeResult={}",employeeResult);
            allCount += dingMappingEmployeeManager.insertModelEmp(employeeResult, appId);
        }

        return Result.newSuccess(allCount);
    }


    private String randomName(String name) {
        Integer suffix = (int) Math.random() * 1000;
        return name.concat("__").concat(suffix.toString());
    }

    private DingMappingEmployeeResult convertMappingResult(CreateCrmEmployeeVo createCrmEmployeeVo, DeptVo deptVo) {
        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
        result.setEmployeeId(createCrmEmployeeVo.getCrmEmpId());
        result.setDingEmployeeName(createCrmEmployeeVo.getName());
        result.setDingDeptId(deptVo.getDingDeptId());
        result.setDingDeptName(deptVo.getName());
        result.setCrmDeptId(deptVo.getCrmDeptId());
        result.setDingUnionId(createCrmEmployeeVo.getUnionid());
        result.setDingEmployeePhone(createCrmEmployeeVo.getMobile());
        result.setDingEmployeeId(createCrmEmployeeVo.getDingEmployeeId());
        result.setEmployeeName(createCrmEmployeeVo.getName());
        result.setEi(createCrmEmployeeVo.getEi());
        result.setDingEmployeeStatus(1);
        result.setEmployeeStatus(1);
        result.setBindStatus(2);//已绑定
        return result;
    }


    //递归获取附属部门
    public List<Integer> treeDeptIds(Map<Long, DeptVo> deptMaps, Long dingDeptId, List<Integer> ids) {
//        List<Long> ids = new ArrayList<>();
        if ((null != dingDeptId) && (deptMaps.get(dingDeptId) != null)) {
            Integer crmDeptId = deptMaps.get(dingDeptId).getCrmDeptId();
            Long dingDeptID = deptMaps.get(dingDeptId).getDingParentId();
            ids.add(crmDeptId.intValue());
            treeDeptIds(deptMaps, dingDeptID, ids);
        }
        return ids;
    }

    public  static String employeeValidName(String name) {
        String match = "[^\\-·\\[\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name.trim());
        String result = m.replaceAll("-");
        return result;
    }

    @Override
    public Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo(Integer ei, Integer userId) {
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoListResult = fsEmployeeServiceProxy.batchGetEmployeeDto(ei, Lists.newArrayList(userId));
        if(!employeeDtoListResult.isSuccess()) {
            return Result.newError(employeeDtoListResult.getCode(), employeeDtoListResult.getMsg());
        }
        com.facishare.open.order.contacts.proxy.api.result.Result<BatchGetRoleCodesByEmployeeIds.Result> employeeRoleCodesResult = fsEmployeeServiceProxy.batchGetEmployeeRoleCodes(ei, Lists.newArrayList(userId));
        if(!employeeRoleCodesResult.isSuccess()) {
            return Result.newError(employeeRoleCodesResult.getCode(), employeeRoleCodesResult.getMsg());
        }
        FsEmployeeDetailInfo info = new FsEmployeeDetailInfo();
        info.setEnterpriseId(ei);
        info.setEmployeeId(userId);
        info.setFullName(employeeDtoListResult.getData().get(0).getFullName());
        info.setName(employeeDtoListResult.getData().get(0).getName());
        info.setRoleCodes(employeeRoleCodesResult.getData().getEmployeeIdRolesMap().get(String.valueOf(userId)));
        return Result.newSuccess(info);
    }

}
