package com.facishare.open.ding.api.result;

import com.facishare.open.ding.api.arg.CrmProductCategoryArg;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CrmProductCategoryResult extends CrmProductCategoryArg {
    //子级产品分类，用来拼装产品分类的层级结构，没有其它用处
    private List<CrmProductCategoryResult> children = new ArrayList<>();
    //在遍历的过程中，动态生态的层级路径，类似 层级1-->层级1.1-->层级1.1.1
    private String levelPathName;
}
