package com.facishare.open.ding.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/27 15:42
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MoreAppCrmCountModel implements Serializable {
    private String dingGoodsCode;
    private String dingItemCode;
    private Integer proCrmAccount;//crm专业版赠送人数
    private Integer strengthAccount;//
    private Integer schemaAccount;//应用本身自己的账号 如：订货通下游的端口 营销通的虚拟账号。不是属于crm账号的，可以通过该变量控制订单的quantity
    private Boolean hasSyncRealAccount;//是否同步真实的订单数据
}
