package com.facishare.open.ding.transfer.handler;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.provider.entity.DingMappingEmployee;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 钉钉ISV员工信息处理器
 * <AUTHOR>
 * @date 2024/3/7
 */
@Slf4j
@Component
public class DingTalkIsvEmployeeInfoHandler extends DingTalkIsvHandler<DingMappingEmployee, List<OuterOaEmployeeDataEntity>> {

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;

    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;

    @Autowired
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;

    @Override
    protected void transferAfter(int enterpriseId) throws Throwable {
        appAuthEntityCache.invalidate(enterpriseId);
    }

    @Override
    protected void update(int enterpriseId, DingMappingEmployee sourceData, List<OuterOaEmployeeDataEntity> targetData) {
        final Map<String, OuterOaEmployeeDataEntity> collect = targetData.stream()
                .collect(Collectors.toMap(OuterOaEmployeeDataEntity::getOutEa, Function.identity()));
        final List<OuterOaEnterpriseBindEntity> list = appAuthEntityCache.get(sourceData.getEi());

        for (OuterOaEnterpriseBindEntity entity : list) {
            final OuterOaEmployeeDataEntity target = collect.get(entity.getOutEa());
            DingTalkEmployeeObject entityData = convert2PgData(sourceData, target);
            if (Objects.nonNull(entityData)) {
                outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(entityData), ChannelEnum.dingding, entity.getId());
            }
        }
    }

    private DingTalkEmployeeObject convert2PgData(DingMappingEmployee sourceData, OuterOaEmployeeDataEntity targetData) {
        if (Objects.nonNull(targetData)) {
            final DingTalkEmployeeObject employeeInfo = targetData.getOutUserInfo().toJavaObject(DingTalkEmployeeObject.class);
            if (checkEquals(sourceData, employeeInfo)) {
                return null;
            }
        }

        // 构建员工信息
        DingTalkEmployeeObject employeeInfo = new DingTalkEmployeeObject();
        employeeInfo.setUserid(sourceData.getDingEmployeeId());
        employeeInfo.setStatus(sourceData.getDingEmployeeStatus());
        employeeInfo.setName(sourceData.getDingEmployeeName());
        employeeInfo.setPhone(sourceData.getDingEmployeePhone());
        employeeInfo.setUnionId(sourceData.getDingUnionId());
        employeeInfo.setDeptId(sourceData.getDingDeptId());
        employeeInfo.setDeptName(sourceData.getDingDeptName());
        employeeInfo.setPosition(sourceData.getDingEmployeePosition());
        employeeInfo.setJobNumber(sourceData.getDingJobNumber());
        employeeInfo.setManager_userid(sourceData.getDingEmployeeId());
        employeeInfo.setEmail(sourceData.getDingEmployeeEmail());
        employeeInfo.setSexType(sourceData.getDingSexType());

        return employeeInfo;
    }

    @Override
    protected boolean checkDataEquals(DingMappingEmployee sourceData, List<OuterOaEmployeeDataEntity> targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        final List<OuterOaEnterpriseBindEntity> list = appAuthEntityCache.get(sourceData.getEi());
        if (CollectionUtils.isEmpty(targetData) || targetData.size() < list.size()) {
            return false;
        }

        final Set<String> outEas = list.stream().map(OuterOaEnterpriseBindEntity::getOutEa).collect(Collectors.toSet());
        final Set<String> newOutEas = targetData.stream().map(OuterOaEmployeeDataEntity::getOutEa).collect(Collectors.toSet());
        if (!newOutEas.containsAll(outEas)) {
            return false;
        }

        final String outEa = outEas.stream().findFirst().orElse(null);
        final DingTalkEmployeeObject dingTalkEmployeeObject = targetData.stream()
                .filter(entity -> Objects.equals(entity.getOutEa(), outEa))
                .map(OuterOaEmployeeDataEntity::getOutUserInfo)
                .map(json -> json.toJavaObject(DingTalkEmployeeObject.class))
                .findFirst().orElse(null);

        return checkEquals(sourceData, dingTalkEmployeeObject);
    }

    private boolean checkEquals(DingMappingEmployee sourceData, DingTalkEmployeeObject targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        return Objects.equals(targetData.getStatus(), sourceData.getDingEmployeeStatus()) &&
                Objects.equals(targetData.getName(), sourceData.getDingEmployeeName()) &&
                Objects.equals(targetData.getPhone(), sourceData.getDingEmployeePhone()) &&
                Objects.equals(targetData.getUnionId(), sourceData.getDingUnionId()) &&
                Objects.equals(targetData.getDeptId(), sourceData.getDingDeptId()) &&
                Objects.equals(targetData.getDeptName(), sourceData.getDingDeptName()) &&
                Objects.equals(targetData.getPosition(), sourceData.getDingEmployeePosition()) &&
                Objects.equals(targetData.getJobNumber(), sourceData.getDingJobNumber()) &&
                Objects.equals(targetData.getEmail(), sourceData.getDingEmployeeEmail()) &&
                Objects.equals(targetData.getSexType(), sourceData.getDingSexType());
    }

    @Override
    protected List<OuterOaEmployeeDataEntity> getTargetData(int enterpriseId, DingMappingEmployee k) {
        final List<String> outEas = appAuthEntityCache.get(enterpriseId).stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .distinct()
                .collect(Collectors.toList());

        return outerOaEmployeeDataMapper.queryByChannelAndUserIdAndOutEas(
                ChannelEnum.dingding.name(),
                k.getDingEmployeeId(),
                outEas
        );
    }

    @Override
    protected List<DingMappingEmployee> getSourceDataPage(Integer enterpriseId, DingMappingEmployee maxId) {
        Long id = Objects.isNull(maxId) ? null : maxId.getId();
        return dingMappingEmployeeDao.pageById(enterpriseId, id, 1000);
    }

    @Override
    public int getThreadNum() {
        return 10;
    }
}