package com.facishare.open.ding.api.service;

import com.facishare.eservice.rest.cases.model.DingObjectDataListModel;
import com.facishare.open.ding.api.arg.ComponentQueryArg;
import com.facishare.open.ding.api.model.ErpPushDataObj;
import com.facishare.open.ding.api.result.DingStorageResult;
import com.facishare.open.ding.common.result.Result;

/**
 * <AUTHOR>
 * @Date 2022/1/20 15:55 提供钉钉的数据源
 * @Version 1.0
 */
public interface DataStorageService {
     DingStorageResult<DingObjectDataListModel.Result> getEserviceWorkData(ComponentQueryArg componentQueryArg);

     Result<String> createPersonCustomer(ErpPushDataObj erpPushDataObj,Integer tenantId);
}
