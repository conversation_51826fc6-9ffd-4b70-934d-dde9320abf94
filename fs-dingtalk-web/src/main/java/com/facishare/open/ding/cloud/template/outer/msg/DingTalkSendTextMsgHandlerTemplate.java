package com.facishare.open.ding.cloud.template.outer.msg;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.arg.SendCardMessageArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextMessageResult;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉发送文本消息模板
 * 
 * <AUTHOR>
 * @date 20240926
 */
@Slf4j
@Component
// IgnoreI18nFile
public class DingTalkSendTextMsgHandlerTemplate extends SendMsgHandlerTemplate {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingManager dingManager;

    @Override
    protected void filterMsg(MethodContext context) {
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void buildMsg(MethodContext context) {
        log.info("DingTalkSendTextMsgHandlerTemplate.buildMsg,context={}", context);
        SendTextMessageArg sendTextMessageArg = context.getData();
        log.info("DingTalkSendTextMsgHandlerTemplate.buildMsg,sendTextMessageArg={}", sendTextMessageArg);

        Integer ei = sendTextMessageArg.getEi();
        SendTextMessageResult result = new SendTextMessageResult();
        result.setCode(200);
        result.setMessage("发送成功");

        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(ei);
        if (CollectionUtils.isEmpty(corpResult.getData())) {
            log.info("DingTalkSendTextMsgHandlerTemplate.buildMsg,queryByEi,ei not bind,ei={}", ei);
            context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
            return;
            // return result;
        }
        String dingCorpId = corpResult.getData().get(0).getDingCorpId();
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID,
                null);
        if (ObjectUtils.isEmpty(appAuthResult.getData())) {
            log.info("DingTalkSendTextMsgHandlerTemplate.buildMsg,conditionAppAuth,enterprise not support send todo={}",
                    dingCorpId);
            context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
            return;
            // return result;
        }
        Long agentId = appAuthResult.getData().get(0).getAgentId();
        List<Integer> userIds = sendTextMessageArg.getReceiverIds();

        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(ei, userIds, String.valueOf(ConfigCenter.APP_CRM_ID));
        if (CollectionUtils.isEmpty(dataResult.getData())) {
            log.info("DingTalkSendTextMsgHandlerTemplate.buildMsg,batchQueryMapping,crm no user bind ea={},userIds={}",
                    sendTextMessageArg.getEa(), sendTextMessageArg.getReceiverIds());
            context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
            return;
            // return result;
        }
        List<String> dingEmpIds = dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId)
                .collect(Collectors.toList());
        Map<String, Object> argMap = Maps.newHashMap();
        String empIds = Joiner.on(",").join(dingEmpIds);
        argMap.put("userIdList", empIds);
        String message = sendTextMessageArg.getMessageContent();
        argMap.put("message",
                "<font color=\"#A2A3A5\" style=\"line-height:22px;font-size:16px;\">" + message + "/<font>");

        SendCardMessageArg arg = new SendCardMessageArg();
        arg.setAgentId(agentId);
        arg.setDingCorpId(dingCorpId);
        arg.setDingMessageArg(argMap);
        arg.setTemplateId(ConfigCenter.MESSAGE_TEXT_ID);
        arg.setIsCardMessage(false);
        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);

        // //dingManager.sendCardMessage(agentId, dingCorpId, argMap,
        // ConfigCenter.MESSAGE_TEXT_ID, false,ConfigCenter.CRM_SUITE_ID);
        // return result;

        context.setResult(TemplateResult.newSuccess());
        context.setData(arg);
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("DingTalkSendTextMsgHandlerTemplate.sendMsg,context={}", context);
        SendCardMessageArg arg = context.getData();
        Result<String> sendResult = dingManager.sendCardMessage(arg);
        log.info("DingTalkSendTextMsgHandlerTemplate.sendMsg,sendResult={}", sendResult);

        SendTextMessageResult result = new SendTextMessageResult();
        result.setCode(200);
        result.setMessage("发送成功");

        context.setResult(TemplateResult.newSuccess(result));
    }
}
