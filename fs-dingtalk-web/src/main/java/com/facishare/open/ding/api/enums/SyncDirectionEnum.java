package com.facishare.open.ding.api.enums;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/18 14:43
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public enum SyncDirectionEnum {

    /** from 纷享 **/
    FROM_FXIAOKE(1),

    /** to 纷享 **/
    TO_FXIAOKE(2);

    private int type;

    SyncDirectionEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static boolean isInvalid(Integer type) {
        if (type != null) {
            for (SyncDirectionEnum syncTypeEnum : SyncDirectionEnum.values()) {
                if (syncTypeEnum.getType() == type) {
                    return false;
                }
            }
        }
        return true;
    }

}
