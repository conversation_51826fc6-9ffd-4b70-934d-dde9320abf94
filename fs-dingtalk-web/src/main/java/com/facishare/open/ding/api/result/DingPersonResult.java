package com.facishare.open.ding.api.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/13 21:07
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingPersonResult implements Serializable {
    /**
     * 钉钉unionID
     **/
    private String unionId;
    /**
     * 钉钉openId
     **/
    private String openId;
    private String email;
    /**
     * 钉钉昵称
     **/
    private String nick;
    /**
     * 服务窗企业
     **/
    private String upstreamCorpId;

    private Integer upstreamEnterpriseId;
    private String mobile;
    /**
     * //头像地址
     */
    private String avatarUrl;
    /**
     * stateCode 手机号对应的国家号
     */
    private String statCode;


}
