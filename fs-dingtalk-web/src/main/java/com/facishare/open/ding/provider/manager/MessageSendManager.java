package com.facishare.open.ding.provider.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.arg.DingMessageArg;
import com.facishare.open.ding.api.enums.CrmMessageEnum;
import com.facishare.open.ding.api.result.BaseResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.utils.HttpRequestUtils;
import com.facishare.open.ding.provider.arg.BaseExternalMessageArg;
import com.facishare.open.ding.provider.arg.TextCardMessageArg;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.restful.common.StopWatch;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/10/14 11:21
 * @Version 1.0
 */
@Slf4j
@Component
// IgnoreI18nFile
public class MessageSendManager {

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    @Autowired
    private TokenManager tokenManager;

    @ReloadableProperty("mid.authorize.url")
    private String MID_URL;// = "https://www.fxiaoke.com/dingtalk/business/authorize?direct_uri=";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    //网页版
    private static final String DING_SINGLE_URL_WEB = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_login&state=STATE";

    private static final String MESSAGE_TYPE = "action_card";

    private static final String TEXT_TYPE = "markdown";
    private static final String TOKEN_INVALID_CODE = "88";

    @ReloadableProperty("sso.redirect.url.source")
    private String ssoRedirectUrlSource;

    //统一发送钉钉的卡片消息
    public BaseResult commonSendMenssage(BaseExternalMessageArg arg, DingEnterpriseResult enterpriseResult) {
        StopWatch watch = StopWatch.create("trace commonSendMenssage" + arg.getEi());
        log.info("commonSendMessage messageArg:{}", arg);
        BaseResult result = new BaseResult();
        result.setCode(200);

        if (Objects.isNull(arg)) {
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        List<Integer> empUsers = arg.getReceiverIds();
        if (CollectionUtils.isEmpty(empUsers)) {
            log.warn("toUser is empty, arg={}.", arg);
            result.setMessage("stoUser is empty");
            return result;
        }
        StringBuilder strBuilder = new StringBuilder();
        for (Integer fxId : empUsers) {
            DingMappingEmployeeResult mappingEmployeeResult = employeeManager.queryMappingEmployeeByEi(ei, fxId, enterpriseResult.getAppKey());
            if (Objects.isNull(mappingEmployeeResult)) {
                log.info("emp not bind,ei={},fxId={}", ei, fxId);
                continue;
            }
            strBuilder.append(mappingEmployeeResult.getDingEmployeeId()).append(",");
        }
        if (strBuilder.length() <= 0) {
            log.info("no user need to send message");
            result.setMessage("no user need to send message");
            return result;
        }
        watch.lap("queryEnter");
        strBuilder.deleteCharAt(strBuilder.length() - 1);
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(enterpriseResult.getClientIp());
        Map<String, Object> messageArg = new HashMap<>();
        String accessToken = tokenManager.getToken(ei, enterpriseResult.getAppKey());
        String proxyMessageUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(accessToken);
        messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
        messageArg.put("type", "POST");
        messageArg.put("token", enterpriseResult.getToken());
        if (arg instanceof TextCardMessageArg) {
            //发送卡片消息
            messageArg.put("data", convertCardMessage((TextCardMessageArg) arg, enterpriseResult, strBuilder.toString()));
        } else {
            //发送文本消息
            messageArg.put("data", convertTextMessage(arg, enterpriseResult, strBuilder.toString()));
        }

        Object messageResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        watch.lap("sendMessage");
        log.info("MessageSend messageArg:{},messageResult:{}", messageArg, messageResult);
        if (Objects.isNull(messageResult)) {
            log.warn("向钉钉发送待办消息失败，messageArg={}", messageArg);
            return result;
        }
        JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());
        if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
            log.warn("向钉钉发送待办消息失败，messageArg={}.", messageArg);
            return result;
        }

        if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
            if (jsonObject.get("errcode").equals(TOKEN_INVALID_CODE)) {
                Object refreshResult = refreshRequest(messageArg, ei, clientUrl, enterpriseResult.getAppKey());
                log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, refreshResult);
            }
            log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, messageResult);

            log.info("向钉钉发送待办消息失败，messageArg={},messageResult={}.", messageArg, messageResult);
        }
        watch.log();
        result.setMessage("发送成功");
        return result;
    }

    private Object refreshRequest(Map<String, Object> messageArg, Integer ei, String clientUrl, String appId) {
        //重新获取token，发送消息
        Gson gson = new Gson();
        tokenManager.invalidate(ei, appId);
        String refreshToken = tokenManager.getToken(ei, appId);
        String refreshUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(refreshToken);
        messageArg.put("url", refreshUrl);//钉钉发送消息的url
        Object refreshResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        return refreshResult;
    }


    private DingMessageArg convertTextMessage(BaseExternalMessageArg baseArg, DingEnterpriseResult result, String receives) {
        //发送文本消息
        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(result.getAgentId());
        dingMessageArg.setUserid_list(receives);
        dingMessageArg.getMsg().setMsgtype(TEXT_TYPE);
//       CrmMessageEnum.getAppId(baseArg.getReceiverChannelType()))
        JSONObject jsonObject = JSONObject.parseObject(baseArg.getReceiverChannelData());
        Object appId = jsonObject.get("appId");
        CrmMessageEnum messageEnum = CrmMessageEnum.getAppId(appId.toString());
        String time = Long.toString(Calendar.getInstance().getTimeInMillis() / 1000);
//       dingMessageArg.getMsg().setMarkdown(new DingMessageArg.MarkDown(messageEnum.getMessageType(),baseArg.getMessageContent()));
        dingMessageArg.getMsg().setMarkdown(new DingMessageArg.MarkDown(messageEnum.getMessageType() + time, "### " + baseArg.getMessageContent()));
        return dingMessageArg;
    }

    private DingMessageArg convertCardMessage(TextCardMessageArg arg, DingEnterpriseResult result, String receives) {
        //发送文本消息
        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(result.getAgentId());
        dingMessageArg.setUserid_list(receives);
        dingMessageArg.getMsg().setMsgtype(MESSAGE_TYPE);

        //优化卡片消息
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(arg.getForm())) {
            for (int i = 0; i < arg.getForm().size(); i++) {
                if (i == 0) {
                    markdown.append("# ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                } else {
                    markdown.append("## ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                }

            }
        } else {
            markdown.append("# ").append(arg.getTitle()).append("\n");
            markdown.append("# ").append(arg.getMessageContent()).append("\n");
        }
        dingMessageArg.getMsg().getAction_card().setMarkdown(!StringUtils.isEmpty(markdown.toString()) ? markdown.toString() : arg.getMessageContent());
        String time = Long.toString(Calendar.getInstance().getTimeInMillis() / 1000);
        //title加上时间戳，避免相同信息钉钉限制发送
        dingMessageArg.getMsg().getAction_card().setTitle(arg.getTitle() + time);
        dingMessageArg.getMsg().getAction_card().setSingle_title("查看详情");
//
        String objectApiName = arg.getExtraDataMap().get("objectApiName");
        String objectId = arg.getExtraDataMap().get("objectId");
        String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
        String directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType();
        StringBuilder stringBuilder = new StringBuilder();
        String directAppId = result.getRedirectAppId();
        stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
        log.info("finalUrl = {}", stringBuilder.toString());
        dingMessageArg.getMsg().getAction_card().setSingle_url(stringBuilder.toString());//需要跳转到纷享的url
        return dingMessageArg;

    }


}
