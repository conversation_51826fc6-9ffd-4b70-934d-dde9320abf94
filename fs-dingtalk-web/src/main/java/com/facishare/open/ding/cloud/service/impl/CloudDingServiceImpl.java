package com.facishare.open.ding.cloud.service.impl;

import com.facishare.open.ding.api.service.cloud.CloudDingService;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.model.UserVo;
import com.facishare.open.ding.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021/5/19 18:43
 * @Version 1.0
 */
public class CloudDingServiceImpl implements CloudDingService {
    @Autowired
    private DingManager dingManager;
    @Override
    public Result<EmployeeDingVo> queryUserInfo(String corpId, String code) {
        return null;
    }
}
