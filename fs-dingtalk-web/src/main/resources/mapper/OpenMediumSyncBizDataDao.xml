<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.facishare.open.ding.cloud.dao.OpenSyncBizDataDao">

    <resultMap id="baseResultMap" type="com.facishare.open.ding.cloud.entity.HighBizDataDo">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="gmt_create" property="gmtCreate" javaType="java.util.Date"/>
        <result column="gmt_modified" property="gmtModified" javaType="java.util.Date"/>
        <result column="subscribe_id" property="subscribeId" javaType="java.lang.String"/>
        <result column="corp_id" property="corpId" javaType="java.lang.String"/>
        <result column="biz_id" property="bizId" javaType="java.lang.String"/>
        <result column="biz_tye" property="bizType" javaType="java.lang.Integer"/>
        <result column="biz_data" property="bizData" javaType="java.lang.Integer"/>
        <result column="open_cursor" property="openCursor" javaType="java.lang.Long"/>
        <result column="status" property="status" javaType="java.lang.Integer"/>
    </resultMap>

    <sql id="baseColumn">
        id,gmt_create,gmt_modified,subscribe_id,corp_id,biz_id,biz_tye,biz_data,open_cursor,status
    </sql>

    <insert id="batchSave">
        INSERT IGNORE INTO open_sync_biz_data
        (gmt_create,gmt_modified,subscribe_id,corp_id,biz_id,biz_type,biz_data,open_cursor,status) values
        <foreach collection="dataDos"  item="item" separator="," index="index">
            (#{item.gmtCreate,jdbcType=TIMESTAMP},#{item.gmtModified,jdbcType=TIMESTAMP},#{item.subscribeId},#{item.corpId},#{item.bizId},#{item.bizType},#{item.bizData},#{item.openCursor},#{item.status})
        </foreach>
        ON DUPLICATE KEY
        UPDATE biz_data=values(biz_data),gmt_modified=values(gmt_modified),open_cursor=values(open_cursor),status=values(status)
    </insert>


</mapper>