<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-3.1.xsd">

       <aop:aspectj-autoproxy/>

       <!-- 日志拦截器 -->
       <bean id="logInterceptor" class="com.facishare.open.ding.common.interceptor.LogInterceptor"/>
       <!-- 异常的统一处理 拦截器 -->
       <bean id="apiExceptionInterceptor" class="com.facishare.open.ding.common.interceptor.ApiExceptionInterceptor"/>
       <!-- 蜂眼监控 -->
       <aop:aspectj-autoproxy proxy-target-class="true"/>
       <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
       <aop:config>
              <aop:aspect id="monitor" ref="serviceProfiler">
                     <aop:pointcut id="pointCutAround"
                                   expression="(execution(* com.facishare.open.ding.web..*.*(..)))"/>
                     <aop:around method="profile" pointcut-ref="pointCutAround"/>
              </aop:aspect>
       </aop:config>
       <aop:config>
              <aop:pointcut id="exceptionMethods" expression="execution(* com.facishare.open.ding.web.controller.*.*(..))"/>
              <aop:pointcut id="logMethods" expression="execution(* com.facishare.open.ding.web.controller.*.*(..)) || @annotation(org.springframework.web.bind.annotation.RestController)"/>

<!--              <aop:advisor order="0" pointcut-ref="logMethods" advice-ref="logInterceptor"/>-->
              <aop:advisor order="1" pointcut-ref="exceptionMethods" advice-ref="apiExceptionInterceptor"/>
       </aop:config>


       <!--fs-dingtalk-provider spring-aop.xml-->
<!--       <aop:aspectj-autoproxy/>-->

<!--       &lt;!&ndash; 日志拦截器 &ndash;&gt;-->
<!--       <bean id="logInterceptor" class="com.facishare.open.ding.common.interceptor.LogInterceptor"/>-->

<!--       &lt;!&ndash; 异常的统一处理 拦截器 &ndash;&gt;-->
<!--       <bean id="apiExceptionInterceptor" class="com.facishare.open.ding.common.interceptor.ApiExceptionInterceptor"/>-->

       <aop:config>
              <aop:pointcut id="serviceMethods" expression="execution(* com.facishare.open.ding.api.service.*.*(..)))"/>
              <aop:pointcut id="serviceAndManagerMethods" expression="execution(* com.facishare.open.ding.provider.*.*.*(..))"/>

<!--              <aop:advisor order="0" pointcut-ref="serviceAndManagerMethods" advice-ref="logInterceptor"/>-->
              <aop:advisor order="1" pointcut-ref="serviceMethods" advice-ref="apiExceptionInterceptor"/>
       </aop:config>
       <!--fs-dingtalk-provider spring-aop.xml-->

       <!--fs-dingtalk-cloud spring-aop.xml-->
<!--       <aop:aspectj-autoproxy/>-->
<!--       &lt;!&ndash; 蜂眼监控 &ndash;&gt;-->
<!--       <aop:aspectj-autoproxy proxy-target-class="true"/>-->
<!--       <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>-->
       <aop:config>
              <aop:aspect id="monitor" ref="serviceProfiler">
                     <aop:pointcut id="pointCutAround"
                                   expression="(execution(* com.facishare.open.ding.cloud..*.*(..)))"/>
                     <aop:around method="profile" pointcut-ref="pointCutAround"/>
              </aop:aspect>
       </aop:config>
<!--       &lt;!&ndash; 日志拦截器 &ndash;&gt;-->
<!--       <bean id="logInterceptor" class="com.facishare.open.ding.common.interceptor.LogInterceptor"/>-->

<!--       &lt;!&ndash; 异常的统一处理 拦截器 &ndash;&gt;-->
<!--       <bean id="apiExceptionInterceptor" class="com.facishare.open.ding.common.interceptor.ApiExceptionInterceptor"/>-->

       <aop:config>
              <aop:pointcut id="serviceMethods" expression="execution(* com.facishare.open.ding.cloud..*.*(..))"/>
              <aop:pointcut id="serviceAndManagerMethods" expression="!execution(* com.facishare.open.ding.cloud.service.impl.connector..*.*(..))  AND execution(* com.facishare.open.ding.cloud..*.*(..)) AND execution(* com.facishare.marketing.outapi.service.DingAddressBookCallBackService.*(..))"/>

<!--              <aop:advisor order="0" pointcut-ref="serviceAndManagerMethods" advice-ref="logInterceptor"/>-->
              <aop:advisor order="1" pointcut-ref="serviceMethods" advice-ref="apiExceptionInterceptor"/>
       </aop:config>
       <!--fs-dingtalk-cloud spring-aop.xml-->

</beans>