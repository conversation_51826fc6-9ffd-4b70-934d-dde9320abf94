<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:protocol id="dubbo" name="dubbo" port="${dubbo.provider.port}"/>
    <dubbo:provider  timeout="15000" filter="tracerpc" />

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.AuthService" ref="authServiceImpl" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.EnterpriseService" ref="enterpriseService" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.ObjectMappingService" ref="objectMappingService" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.CloudEmpService" ref="cloudEmpServiceImpl" protocol="dubbo" version="${dubbo.provider.version}"/>-->
<!--    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.CloudAccountService" ref="cloudAccountServiceImpl" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.RedisDingService" ref="redisDingService" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.SyncLogService" ref="syncLogService" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.DingtalkUserService" ref="dingtalkUserService" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.fxiaoke.message.extrnal.platform.api.ExternalMessageService" ref="externalMessageServiceImpl" group="dingtalk" protocol="dubbo"  timeout="15000"/>-->

<!--    <dubbo:service interface="com.fxiaoke.message.extrnal.platform.api.ExternalTodoService" ref="externalTodoServiceImpl" group="dingtalk" protocol="dubbo"  timeout="15000"/>-->

<!--    <dubbo:service id="externalOaTodoService" ref="externalOaTodoServiceImpl" interface="com.facishare.open.webhook.messagesend.service.ExternalOaTodoService" group="dingtalk" protocol="dubbo" />-->

<!--    <dubbo:service id="dingCorpMappingService" ref="dingCorpMappingServiceImpl" interface="com.facishare.open.ding.api.service.DingCorpMappingService"  protocol="dubbo" version="1.0" />-->

<!--    <dubbo:service id="appAuthService" ref="appAuthServiceImpl" interface="com.facishare.open.ding.api.service.AppAuthService" protocol="dubbo" version="1.0" />-->

<!--    <dubbo:service id="userAppMappingService" ref="userAppMappingServiceImpl" interface="com.facishare.open.ding.api.service.UserAppMappingService" protocol="dubbo" version="1.0" />-->

<!--    <dubbo:service id="pollingSyncService" ref="pollingSyncServiceImpl" interface="com.facishare.open.ding.api.service.PollingSyncService" protocol="dubbo" version="1.0" />-->
<!--    <dubbo:service id="dingRefuseDataService" ref="dingRefuseDataServiceImpl" interface="com.facishare.open.ding.api.service.cloud.DingRefuseDataService" protocol="dubbo" version="1.0" />-->



<!--    <dubbo:service id="cloudDeptService" ref="cloudDeptServiceImpl" interface="com.facishare.open.ding.api.service.cloud.CloudDeptService" protocol="dubbo" version="1.0" />-->

<!--    <dubbo:service id="cloudOrderService" ref="cloudOrderServiceImpl" interface="com.facishare.open.ding.api.service.cloud.CloudOrderService" protocol="dubbo" version="1.0" />-->

<!--    <dubbo:service id="crmSyncObjService" ref="crmSyncObjServiceImpl" interface="com.facishare.open.ding.api.service.CrmSyncObjService" protocol="dubbo" version="1.0" />-->
<!--    <dubbo:service id="dingObjSyncService" ref="dingObjSyncServiceImpl" interface="com.facishare.open.ding.api.service.DingObjSyncService" protocol="dubbo" version="1.0" />-->

<!--    <dubbo:service id="connectorObjectDataCacheService"-->
<!--                   ref="connectorObjectDataCacheServiceImpl"-->
<!--                   interface="com.facishare.open.ding.api.service.cloud.connector.ConnectorObjectDataCacheService"-->
<!--                   protocol="dubbo"-->
<!--                   version="1.0" />-->
<!--    <dubbo:service id="dingMappingEmployeeService"-->
<!--                   ref="dingMappingEmployeeServiceImpl"-->
<!--                   interface="com.facishare.open.ding.api.service.DingMappingEmployeeService"-->
<!--                   protocol="dubbo"-->
<!--                   version="1.0" />-->

<!--    <dubbo:service id="objectWriterService"-->
<!--                   ref="crmObjectWriteServiceImpl"-->
<!--                   interface="com.facishare.open.ding.api.service.CrmObjectWriterService"-->
<!--                   protocol="dubbo"-->
<!--                   version="1.0" />-->

<!--    <dubbo:service id="syncDataMappingsService"-->
<!--                   ref="syncDataMappingsServiceImpl"-->
<!--                   interface="com.facishare.open.ding.api.service.SyncDataMappingsService"-->
<!--                   protocol="dubbo"-->
<!--                   version="1.0" />-->
<!--    <dubbo:service id="dssSyncDataMappingService"-->
<!--                   ref="dssSyncDataMappingServiceImpl"-->
<!--                   interface="com.facishare.open.ding.api.service.DssSyncDataMappingService"-->
<!--                   protocol="dubbo"-->
<!--                   version="1.0" />-->

    <!--fs-dingtalk-cloud spring-provider.xml-->
<!--    <dubbo:service interface="com.facishare.open.ding.api.service.SyncBizDataService" ref="syncBizDataServiceImpl" protocol="dubbo" version="1.0"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.CloudDingRequestService" ref="cloudDingRequestServiceImpl" protocol="dubbo" version="1.0"/>-->
<!--    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.DingProxyService" ref="dingProxyServiceImpl" protocol="dubbo" version="1.0"/>-->
    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.DingAuthService" ref="dingAuthServiceImpl" protocol="dubbo" version="1.0"/>
    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.DingSupportSendMessageService" ref="dingSupportSendMessageServiceImpl" protocol="dubbo" version="1.0"/>


    <dubbo:service interface="com.fxiaoke.message.extrnal.platform.api.ExternalTodoService" ref="cloudExternalTodoServiceImpl" group="dingCloudGroup" protocol="dubbo"  timeout="15000"/>
    <dubbo:service interface="com.fxiaoke.message.extrnal.platform.api.ExternalMessageService" ref="cloudExternalMessageServiceImpl" group="dingCloudGroup" protocol="dubbo"  timeout="15000"/>
<!--    <dubbo:service interface="com.facishare.open.ding.api.service.DataStorageService" ref="dataStorageServiceImpl"  version="1.0" protocol="dubbo"  timeout="15000"/>-->
<!--    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.DingObjectService" ref="dingObjectServiceImpl" version="1.0" protocol="dubbo" timeout="15000"/>-->

<!--    <dubbo:service-->
<!--            id="connectorSyncObjectService"-->
<!--            ref="connectorSyncObjectServiceImpl"-->
<!--            interface="com.facishare.open.ding.api.service.cloud.connector.ConnectorSyncObjectService"-->
<!--            protocol="dubbo"-->
<!--            version="1.0"-->
<!--            timeout="60000" />-->

<!--    <dubbo:service-->
<!--            id="cloudToolsService"-->
<!--            ref="cloudToolsServiceImpl"-->
<!--            interface="com.facishare.open.ding.api.service.cloud.CloudToolsService"-->
<!--            protocol="dubbo"-->
<!--            version="1.0"-->
<!--            timeout="60000" />-->

<!--    <dubbo:service-->
<!--            id="cloudNotificationService"-->
<!--            ref="cloudNotificationServiceImpl"-->
<!--            interface="com.facishare.open.ding.api.service.cloud.CloudNotificationService"-->
<!--            protocol="dubbo"-->
<!--            version="1.0"-->
<!--            timeout="60000" />-->

<!--    <dubbo:service-->
<!--            id="cloudMonitorService"-->
<!--            ref="cloudMonitorServiceImpl"-->
<!--            interface="com.facishare.open.ding.api.service.cloud.CloudMonitorService"-->
<!--            protocol="dubbo"-->
<!--            version="1.0"-->
<!--            timeout="60000" />-->

<!--    <dubbo:service-->
<!--            id="cloudContactsService"-->
<!--            ref="cloudContactsServiceImpl"-->
<!--            interface="com.facishare.open.ding.api.service.cloud.CloudContactsService"-->
<!--            protocol="dubbo"-->
<!--            version="1.0"-->
<!--            timeout="60000" />-->
    <!--fs-dingtalk-cloud spring-provider.xml-->



    <dubbo:service interface="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"
                   ref="dingTalkIsvManager"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   retries="1"/>
</beans>