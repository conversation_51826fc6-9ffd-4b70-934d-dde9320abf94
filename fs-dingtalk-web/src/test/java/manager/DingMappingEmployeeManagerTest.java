//package manager;
//
//import base.BaseAbstractTest;
//import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
//import com.facishare.open.ding.api.vo.DeptVo;
//import com.facishare.open.ding.common.result.Result;
//import com.facishare.open.ding.provider.dingding.DingRequestUtil;
//import com.facishare.open.ding.provider.entity.DingEnterprise;
//import com.facishare.open.ding.provider.manager.DingDeptMananger;
//import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
//import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
//import com.github.mybatis.pagination.Page;
//import com.google.gson.Gson;
//import lombok.extern.slf4j.Slf4j;
//import org.checkerframework.checker.units.qual.A;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <p></p>
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2018/7/16 11:05
// */
//@Slf4j
//public class DingMappingEmployeeManagerTest extends BaseAbstractTest{
//    @Autowired
//    private DingEnterpriseManager dingEnterpriseManager;
//    @Autowired
//    private DingMappingEmployeeManager dingMappingEmployeeManager;
//    @Autowired
//    private DingDeptMananger dingDeptMananger;
//
//    @Test
//    public void testSaveEnterprise(){
//        DingEnterprise dingEnterprise = new DingEnterprise();
//        dingEnterprise.setEa("1");
//        dingEnterprise.setEi(1);
//        dingEnterprise.setAgentId("asd");
//        dingEnterprise.setAppKey("1234");
//        dingEnterprise.setDingCorpId("1234QAERFQWE");
//        dingEnterpriseManager.saveEnterprise(dingEnterprise);
//    }
//
//
//    @Test
//    public void conditionQuery(){
//        Result<List<DingMappingEmployeeResult>> listResult = dingMappingEmployeeManager.conditionQueryEmployee(76235, null, 1, 20, "", null, null);
//        if (listResult.isSuccess()) {
//            System.out.println(listResult.getData());
//        }
//    }
//
//    @Test
//    public void conditionEmployeeCount(){
//        Integer count = dingMappingEmployeeManager.conditionEmployeeCount(76235, null, "", null, null);
//        System.out.println(count);
//    }
//
//
//    @Test
//    public void testDingMapping(){
//        //注册回调接口
//        String registUrl = DingRequestUtil.appendCallBackUrl("http://47.113.195.161:8089/");
//        Map<String, Object> registArg = new HashMap<>();
//        registArg.put("ei", 76246);
//        registArg.put("callBackUrl", "https://www.ceshi112.com/dingtalk/business/callback?ei=76246");
//        registArg.put("appKey", "dingskqye5ljk4pogfbf");
//        registArg.put("appSecret", "D4F0V5a8-HRUvXck3LDtP_AGw7UDQP_D_0k2l8kmttFOURFIYyrJkUcBGvvt_OwL");
//        Gson gson=new Gson();
//        Object registResult = DingRequestUtil.proxyRequest(registUrl, gson.toJson(registArg));
//    }
//
//    @Test
//    public void testGetEmpTotalCount() {
//        // 测试正常场景
//        Integer count = dingMappingEmployeeManager.getEmpTotalCount(76235, 1, "张三", "***********", "testAppId");
//        System.out.println("Total count: " + count);
//
//        // 测试参数错误场景
//        Integer errorCount = dingMappingEmployeeManager.getEmpTotalCount(null, 1, "张三", "***********", "testAppId");
//        assert errorCount == 0;
//    }
//
//    @Test
//    public void testGetEmpAllCount() {
//        // 测试正常场景
//        Integer count = dingMappingEmployeeManager.getEmpAllCount(76235, "testAppId");
//        System.out.println("All count: " + count);
//
//        // 测试参数错误场景
//        Integer errorCount = dingMappingEmployeeManager.getEmpAllCount(null, "testAppId");
//        assert errorCount == 0;
//    }
//
//    @Test
//    public void testSaveMappingEmployee() {
//        // 构造测试数据
//        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
//        result.setEi(76235);
//        result.setDingEmployeeId("test123");
//        result.setEmployeeId(1001);
//        result.setDingEmployeeName("测试员工");
//        result.setDingEmployeePhone("***********");
//
//        // 测试正常场景
//        Integer saveResult = dingMappingEmployeeManager.saveMappingEmployee(result, "testAppId");
//        System.out.println("Save result: " + saveResult);
//
//        // 测试参数错误场景
//        Integer errorResult = dingMappingEmployeeManager.saveMappingEmployee(null, "testAppId");
//        assert errorResult == 0;
//    }
//
//    @Test
//    public void testRelieveBind() {
//        // 测试正常场景
//        Integer result = dingMappingEmployeeManager.relieveBind(76235, "test123", "testAppId");
//        System.out.println("Relieve bind result: " + result);
//
//        // 测试参数错误场景
//        Integer errorResult = dingMappingEmployeeManager.relieveBind(null, "test123", "testAppId");
//        assert errorResult == 0;
//    }
//
//    @Test
//    public void testDeleteBind() {
//        // 测试正常场景
//        Integer result = dingMappingEmployeeManager.deleteBind(76235, "test123", "testAppId");
//        System.out.println("Delete bind result: " + result);
//
//        // 测试参数错误场景
//        Integer errorResult = dingMappingEmployeeManager.deleteBind(null, "test123", "testAppId");
//        assert errorResult == 0;
//    }
//
//    @Test
//    public void testDeleteBindByFsId() {
//        // 测试正常场景
//        Integer result = dingMappingEmployeeManager.deleteBindByFsId(76235, 1001, "testAppId");
//        System.out.println("Delete bind by fs id result: " + result);
//
//        // 测试参数错误场景
//        Integer errorResult = dingMappingEmployeeManager.deleteBindByFsId(null, 1001, "testAppId");
//        assert errorResult == 0;
//    }
//
//    @Test
//    public void testDeleteByDeptId() {
//        // 测试正常场景
//        Integer result = dingMappingEmployeeManager.deleteByDeptId(76235, "test123", 123L, "testAppId");
//        System.out.println("Delete by dept id result: " + result);
//
//        // 测试参数错误场景
//        Integer errorResult = dingMappingEmployeeManager.deleteByDeptId(null, "test123", 123L, "testAppId");
//        assert errorResult == 0;
//    }
//
//    @Test
//    public void testUpdateMappingEmployee() {
//        // 构造测试数据
//        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
//        result.setEi(76235);
//        result.setDingEmployeeId("test123");
//        result.setEmployeeId(1001);
//        result.setBindStatus(1);
//
//        // 测试正常场景
//        Integer updateResult = dingMappingEmployeeManager.updateMappingEmployee(result, "testAppId");
//        System.out.println("Update result: " + updateResult);
//
//        // 测试参数错误场景
//        Integer errorResult = dingMappingEmployeeManager.updateMappingEmployee(null, "testAppId");
//        assert errorResult == 0;
//    }
//
//}
