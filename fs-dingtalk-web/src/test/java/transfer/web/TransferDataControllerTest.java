package transfer.web;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.transfer.web.TransferDataController;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/2 17:16:09
 */
public class TransferDataControllerTest extends BaseAbstractTest {

    @Autowired
    private TransferDataController transferDataController;

    @Test
    public void transferData() throws Exception {
        final Map<String, List<String>> stringListMap = transferDataController.transferData(JSON.parseObject("{\n" +
                "  \"eis\": [\n" +
                "  ],\n" +
                "  \"allTenant\": true,\n" +
                "  \"allHandler\": true,\n" +
                "  \"forceTransfer\": false,\n" +
                "  \"endTime\": null\n" +
                "}", TransferDataController.TransferArg.class));
        System.out.println("======================");
        System.out.println(stringListMap);
    }

    @Test
    public void transferData2() throws Exception {
        final Map<String, List<String>> stringListMap = transferDataController.transferData(JSON.parseObject("{\n" +
                "  \"eis\": [\n" +
                "  ],\n " +
                "\"transferHandlerNames\": [\n" +
                "    \"DingTalkIsvCorpMappingHandler\"\n" +
                "  ]," +
                "  \"allTenant\": true,\n" +
                "  \"allHandler\": false,\n" +
                "  \"forceTransfer\": true,\n" +
                "  \"endTime\": null\n" +
                "}", TransferDataController.TransferArg.class));
        System.out.println("======================");
        System.out.println(stringListMap);
    }
    @Test
    public void transferData3() throws Exception {
        final Map<String, List<String>> stringListMap = transferDataController.transferData(JSON.parseObject("{\n" +
                "  \"eis\": [71557\n" +
                "  ],\n" +
                "  \"allTenant\": false,\n" +
                "  \"transferHandlerNames\": [\n" +
                "    \"DingTalkIsvTaskHandler\"" +
                "  ],\n" +
                "  \"allHandler\": false,\n" +
                "  \"forceTransfer\": true,\n" +
                "  \"endTime\": null\n" +
                "}", TransferDataController.TransferArg.class));
        System.out.println("======================");
        System.out.println(stringListMap);
    }
}