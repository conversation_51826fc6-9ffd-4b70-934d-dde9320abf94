package service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.eservice.rest.cases.model.DingObjectDataListModel;
import com.facishare.open.ding.api.arg.*;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.model.*;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.DataStorageService;
import com.facishare.open.ding.api.service.DingObjSyncService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.SyncBizDataService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.service.cloud.DingRefuseDataService;
import com.facishare.open.ding.api.service.cloud.DingSupportSendMessageService;
import com.facishare.open.ding.api.vo.DingObjSyncVo;
import com.facishare.open.ding.cloud.arg.CreateCrmOrderArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.*;
import com.facishare.open.ding.cloud.mq.EnterpriseEventListener;
import com.facishare.open.ding.cloud.task.CrmHistoryDataPushJob;
import com.facishare.open.ding.common.result.Result;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import com.taobao.api.ApiException;
import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/5 16:59
 * @Version 1.0
 */
@Slf4j
public class DingManagerTest extends BaseAbstractTest {
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingSupportSendMessageService dingSupportSendMessageService;
    @Resource
    @Qualifier("dingAuthServiceImplForSelfBuiltApp")
    private DingAuthService dingAuthService;
    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private HttpCloudManager httpManager;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private DingRefuseDataService refuseDataService;
    @Autowired
    private DataPushManager dataPushManager;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DataStorageService dataStorageService;
    @Autowired
    private CrmHistoryDataPushJob crmHistoryDataPushJob;
    @Autowired
    private DataConvertManager dataConvertManager;
    @Autowired
    private DingObjSyncService dingObjSyncService;
    @Autowired
    private EnterpriseEventListener enterpriseEventListener;

    @Test
    public void createJsApiSignature(){
        Result<DingAppResult> result = dingAuthService.createJsApiSignature("https://www.ceshi112.com/hcrm/dingtalk",
                "84801",
                70480L);
        System.out.println(result);
    }

    @Test
    public void testListen(){
        List<MessageExt> list=Lists.newArrayList();
        MessageExt messageExt=new MessageExt();
        EnterpriseAddEvent enterpriseAddEvent=new EnterpriseAddEvent();
        enterpriseAddEvent.setEnterpriseAccount("dtqycs1244");
        enterpriseAddEvent.setEnterpriseId(85223);
        messageExt.setFlag(1);
        messageExt.setBody(toProto(enterpriseAddEvent));
        list.add(messageExt);
        enterpriseEventListener.consumeMessage(list,null);
    }
    public static byte[] toProto(Object data) {
        Schema schema = RuntimeSchema.getSchema(data.getClass());
        return ProtobufIOUtil.toByteArray(data, schema, LinkedBuffer.allocate(512));
    }

    @Test
    public void syncData(){

        crmHistoryDataPushJob.singleEnterpriseSyncData("dingf5adc85ddf64fdadf2c783f7214b6d69",84111,"crm_contact",0l,"0",ConfigCenter.SERVICE_SUITE_ID);
    }
    @Test
    public void testAuthInfo() {

        dingManager.getAuthInfo("dinge9ad97cd998113e3ee0f45d8e4f7c288", ConfigCenter.CRM_SUITE_ID);
    }

    @Test
    public void testCode() {
        List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get("0");
        String data = "{\"DT_GOODS_881621847020813_572028\":[{\"chargeChannel\":1,\"crmAccount\":10,\"crmProduct\":\"6115f5d45a6b4000015016a1\",\"hasCrmProduct\":true,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":0}],\"DT_GOODS_881621847020813_572029\":[{\"chargeChannel\":1,\"crmAccount\":10,\"crmProduct\":\"6115f5d45a6b4000015016a1\",\"hasCrmProduct\":true,\"hasRealAccountSync\":true,\"payFee\":0,\"schemaAccount\":0}],\"DT_GOODS_881621847020813_747007\":[{\"chargeChannel\":1,\"crmAccount\":10,\"crmProduct\":\"611623255a6b40000150407c\",\"hasCrmProduct\":true,\"hasRealAccountSync\":true,\"payFee\":0,\"schemaAccount\":0}],\"DT_GOODS_881635417957372_856007\":[{\"chargeChannel\":0,\"crmAccount\":1,\"crmProduct\":\"6108dff309cfdc0001b62e75\",\"hasCrmProduct\":true,\"hasRealAccountSync\":true,\"payFee\":0,\"schemaAccount\":0},{\"chargeChannel\":1,\"crmAccount\":10,\"crmProduct\":\"5c9cc8991c3a230001d9338e\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1},{\"chargeChannel\":2,\"crmAccount\":1,\"crmProduct\":\"6108dff309cfdc0001b62e75\",\"hasCrmProduct\":false,\"hasRealAccountSync\":true,\"payFee\":80000,\"schemaAccount\":0},{\"chargeChannel\":0,\"crmAccount\":10,\"crmProduct\":\"5c90b2a97cfed910a04f9799\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1},{\"chargeChannel\":0,\"crmAccount\":10,\"crmProduct\":\"5c90b2a07cfed910a04f8920\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1}],\"DT_GOODS_881635417957372_865027\":[{\"chargeChannel\":0,\"crmAccount\":1,\"crmProduct\":\"6108dff309cfdc0001b62e75\",\"hasCrmProduct\":true,\"hasRealAccountSync\":true,\"payFee\":0,\"schemaAccount\":0},{\"chargeChannel\":1,\"crmAccount\":10,\"crmProduct\":\"5c9cc8c71c3a230001d934ac\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1},{\"chargeChannel\":2,\"crmAccount\":1,\"crmProduct\":\"6108dff309cfdc0001b62e75\",\"hasCrmProduct\":false,\"hasRealAccountSync\":true,\"payFee\":80000,\"schemaAccount\":0},{\"chargeChannel\":0,\"crmAccount\":10,\"crmProduct\":\"5c90b2a97cfed910a04f9799\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1},{\"chargeChannel\":0,\"crmAccount\":10,\"crmProduct\":\"5c90b2a07cfed910a04f8920\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1},{\"chargeChannel\":0,\"crmAccount\":10,\"crmProduct\":\"5c90b2aa7cfed910a04f9d23\",\"hasCrmProduct\":false,\"hasRealAccountSync\":false,\"payFee\":0,\"schemaAccount\":1}]}";
        Map<String, List<NeighborProductQuantity>> stringListMap = JSONObject.parseObject(data, new TypeReference<Map<String, List<NeighborProductQuantity>>>() {
        });

        Result<DingOutUserResult> result = dingManager.getUserByCode("33ca9640eb1f3f808a9d5d00c79d9b0a", "84625", "ding216ede3869d777b6a1320dcb25e91351");
        log.info("result");
    }


    @Test
    public void testGetDingEmp() {
        dingManager.getDingEmp("dingfeca3fa3352c7d4ca39a90f97fcb1e09", Lists.newArrayList("**********-**********"), "********");
    }

    @Test
    public void testScope() {
        ArrayList<Object> objects = Lists.newArrayList();
        objects.stream().forEach(item -> {
            System.out.println(item);
        });
        log.info("qwq");
    }

    @Test
    public void testListScope() {
        dingManager.getListDeptByScope(Lists.newArrayList(1L), "dingbd54ca2536e971bd35c2f4657eb6378f", ConfigCenter.CRM_SUITE_ID);
    }

    @Test
    public void testJson() {
        String json = "{\n" +
                "    \"auth_user_info\": {\n" +
                "        \"userId\": \"manager863\"\n" +
                "    },\n" +
                "    \"auth_corp_info\": {\n" +
                "        \"corp_type\": 0,\n" +
                "        \"corpid\": \"dingcc7ead824f9fa7e0a39a90f97fcb1e09\",\n" +
                "        \"auth_level\": 0,\n" +
                "        \"auth_channel\": \"4\",\n" +
                "        \"industry\": \"\",\n" +
                "        \"full_corp_name\": \"AJMAN科技\",\n" +
                "        \"corp_name\": \"AJMAN科技\",\n" +
                "        \"invite_url\": \"https://wx-in-i.dingtalk.com/invite-page/weixin.html?bizSource=____source____&corpId=dingcc7ead824f9fa7e0a39a90f97fcb1e09&inviterUid=1B60E48F9F3CE0816B2F679C0AD92F4C\",\n" +
                "        \"auth_channel_type\": \"\",\n" +
                "        \"invite_code\": \"\",\n" +
                "        \"is_authenticated\": false,\n" +
                "        \"license_code\": \"\",\n" +
                "        \"corp_logo_url\": \"\"\n" +
                "    },\n" +
                "    \"syncAction\": \"org_suite_auth\",\n" +
                "    \"auth_scope\": {\n" +
                "        \"errcode\": 0,\n" +
                "        \"condition_field\": [],\n" +
                "        \"auth_user_field\": [\n" +
                "            \"jobnumber\",\n" +
                "            \"isLeader\",\n" +
                "            \"name\",\n" +
                "            \"position\",\n" +
                "            \"isAdmin\",\n" +
                "            \"avatar\",\n" +
                "            \"department\",\n" +
                "            \"userid\",\n" +
                "            \"deviceId\",\n" +
                "            \"isHide\"\n" +
                "        ],\n" +
                "        \"auth_org_scopes\": {\n" +
                "            \"authed_user\": [],\n" +
                "            \"authed_dept\": [\n" +
                "                1\n" +
                "            ]\n" +
                "        },\n" +
                "        \"errmsg\": \"ok\"\n" +
                "    },\n" +
                "    \"auth_info\": {\n" +
                "        \"agent\": [\n" +
                "            {\n" +
                "                \"agentid\": 1178826464,\n" +
                "                \"agent_name\": \"纷享销客测试\",\n" +
                "                \"logo_url\": \"https://static-legacy.dingtalk.com/media/lALPDfmVSfuRoWbNARjNARg_280_280.png\",\n" +
                "                \"appid\": 70480,\n" +
                "                \"admin_list\": [\n" +
                "                    \"manager863\"\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"permanent_code\": \"CsccE6YHm9UZEt2TY-u9t2OVLjyrfz7M2YtcDXzp4YbCNGTS5DzuJZLbC_7O7sb7\",\n" +
                "    \"syncSeq\": \"894DD3DFE04AAF2FA8A4D7DE34\"\n" +
                "}";

        AuthEnterPriseModel.BizData authEnterPriseModel = JSONObject.parseObject(json, new TypeReference<AuthEnterPriseModel.BizData>() {
        });
        System.out.println(authEnterPriseModel);
    }

    @Test
    public void testTime() {

        try {
            Date fromDate = DateUtils.parseDate("2021-05-07 10:33:48", new String[]{"yyyy-MM-dd HH:mm:ss"});
            Long time = fromDate.getTime();
            log.info("time:{}", fromDate.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void sendMessage() {


        /*
send cardMessage arg:{agent_id=**********, data={"markdown":"#流程主题: 新建客户(2021-05-21 00:41)\n#客户名称: 特仑苏\n#
成交状态: 未成交\n#负责人: 柯南颖AJMAN\n","apiname":"AccountObj","instanceId":"60a69151d9f75d41b7679b6c","ei":82379,"taskId":1,"objectId":"60a69151f938e300014f9f50"}, template_id=cef7b8d5fcdd4ab4ba6d4c8038759759, userid_list=[manager7605]},result:{"errc
ode":-1,"errmsg":"系统繁忙","request_id":"57di2mapx2hw"}
 */

        Map<String, Object> argMap = Maps.newHashMap();
        String title = "主题：钉钉测试审批（2021-05-31)";
        String customerTitle = "客户名称";
        String testEnter = "某某的测试企业";
        argMap.put("markdown", "<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">" + title + "</font>\n" +
                "        <br>\n" +
                "        <font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">" + customerTitle + "：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">" + testEnter + "</font>"
        );
        argMap.put("ei", "82379");
        argMap.put("apiname", "AccountObj");
        argMap.put("instanceId", "60a69151d9f75d41b7679b6c11");
        argMap.put("taskId", "1");
        argMap.put("objectId", "60a69151f938e300014f9f501");
        argMap.put("userIdList", "manager76051");
        Result<String> result = dingManager.sendCardMessage(**********L, "dingfeca3fa3352c7d4ca39a90f97fcb1e09", argMap, "cef7b8d5fcdd4ab4ba6d4c8038759759", true, ConfigCenter.CRM_SUITE_ID);
        log.info("result:{}", result);
    }


    @Test
    public void testQueryCode() {
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode("b01be399ad283663bd54cd54a8ac60dc");
        try {
            OapiSnsGetuserinfoBycodeResponse response = client.execute(req, ConfigCenter.SUITE_KEY, ConfigCenter.SUITE_SECRET);
            OapiSnsGetuserinfoBycodeResponse.UserInfo userInfo = response.getUserInfo();
            log.info("userinfo:{}", userInfo);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testMessage() {
        DingSendMessageArg dingSendMessageArg = new DingSendMessageArg();
        dingSendMessageArg.setReceiverUserIds(Lists.newArrayList(1047));
        dingSendMessageArg.setSuiteId("********");
        Map<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("message", "test007");
        dingSendMessageArg.setDataMap(paramsMap);
        dingSendMessageArg.setTemplateId("ba1a582827d44f9c96f48d903eff81e2");
        dingSendMessageArg.setTenantId(82379);
        Result<String> stringResult = dingSupportSendMessageService.sendMessage(dingSendMessageArg);
    }

    @Test
    public void queryInfo() {
//        Result<DingOutUserResult> dingOutUserResultResult = dingAuthService.queryUserByCode("1121", "70480", "ding0c8ee222b9a38265f2c783f7214b6d69");
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get("dingoadabmbclx2awjzig2");
        Result<DingPersonResult> authResult = dingAuthService.queryUserInfoByAuth("e53a69649be83533a79dc4358e5159cc", "", "dingoadabmbclx2awjzig2");
        log.info("result:{}", authResult);
        dingManager.getUserByMe("0d461241daf33f699529ffa7975cedfe1");

    }

    @Test
    public void getSSoLogin() {
        String access_token = getAccessToken("dingfeca3fa3352c7d4ca39a90f97fcb1e09", "********");
        String url = "https://oapi.dingtalk.com/user/getuserinfo?access_token=" + access_token + "&code=" + "63270969d6b63057a2e504228a2fc9de";

        String result = httpManager.getUrl(url, createHeader());
        log.info("result:{}", result);
    }

    public String getAccessToken(String authCorpId, String suiteId) {
        //TODO 获取suiteTicket

        Result<String> result = syncBizDataService.queryToken(authCorpId, suiteId);
        log.info("getAccess token:{}", result);
        return result.getData();
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    @Test
    public void testManager() {
        QueryProductArg queryProductArg = new QueryProductArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setTenantId("79648");
        licenseContext.setAppId("CRM");
        licenseContext.setUserId("-10000");
        queryProductArg.setLicenseContext(licenseContext);
        LicenseVersionResult licenseVersionResult = crmManager.queryCrmLicense(queryProductArg);
        List<ProductVersionPojo> crmVersion = licenseVersionResult.getResult().stream().filter(item -> item.getProductType().equals("0")).collect(Collectors.toList());
        String data = JSONObject.toJSONString(licenseVersionResult);

        log.info("licenseVersion");
    }

    @Test
    public void TestQuantity() {
        Map<String, Integer> crmProductMap = Maps.newHashMap();
        crmProductMap.put("dingtalk_standardpro_edition", 5);
        crmProductMap.put("dingtalk_strengthen_edition", 3);

        NeighborProductQuantity crmProduct = NeighborProductQuantity.builder().crmProduct("609d08d08d72c3000147c51f").hasRealAccountSync(false).schemaAccount(0).crmAccount(10).chargeChannel(1)
                .hasCrmProduct(true).payFee(0l).build();
        NeighborProductQuantity flowProduct = NeighborProductQuantity.builder().crmProduct("609d08d08d72c3000147c51f").hasRealAccountSync(false).schemaAccount(0).crmAccount(10).chargeChannel(1)
                .hasCrmProduct(true).payFee(0l).build();
        List<NeighborProductQuantity> quantity = Lists.newArrayList(crmProduct, flowProduct);
        String dataJson = JSONObject.toJSONString(quantity);


        Map<String, List<NeighborProductQuantity>> ding_map = Maps.newHashMap();
        ding_map.put("DT_GOODS_881635417957372_865027", quantity);
        String result = JSONObject.toJSONString(ding_map);
        log.info("result");
        String data = "{\"DT_GOODS_881635417957372_865027\":[{\"crmAccount\":{\"dingtalk_strengthen_edition\":3,\"dingtalk_standardpro_edition\":5},\"crmProduct\":\"609d08d08d72c3000147c51f\",\"hasRealAccountSync\":false,\"schemaAccount\":0},{\"crmProduct\":\"5c9b6b64742c2f6d301caa86\",\"hasRealAccountSync\":false,\"schemaAccount\":10}]}";
        Map<String, List<NeighborProductQuantity>> dataMap = JSONObject.parseObject(data, new TypeReference<Map<String, List<NeighborProductQuantity>>>() {
        });
        log.info("dataMap");

    }


    @Test
    public void updateStatus() {
        Result<Integer> result = refuseDataService.releaseForbid("ding1f33529fa2a5ead424f2f5cc6abecb85");
        log.info("res");
    }

    @Test
    public void queryRole() {
        Result<String> roleResult = dingManager.queryRoleList("dingfeca3fa3352c7d4ca39a90f97fcb1e09", "********");
        log.info("roleResult");
        Result<String> roleResult2 = dingManager.queryRoleList("dinge3998b0f4612fdaaffe93478753d9884", "********");
        log.info("roleResult");
    }

    @Test
    public void testJsonValue() {
        String value = "{\"EventType\":\"SYNC_HTTP_PUSH_MEDIUM\",\"bizData\":[{\"gmt_create\":*************,\"biz_type\":48,\"open_cursor\":0,\"subscribe_id\":\"16\n" +
                "740006_0\",\"id\":65936,\"gmt_modified\":*************,\"biz_id\":\"e540e9b8-b373-45e2-ad0e-9c178b10ef8b\",\"biz_data\":\"{\\\"result\\\":[{\\\"creatorUserId\\\":\\\"manager7605\\\",\\\"gmtModified\\\":1641982350000,\\\"data\\\":{\\\"address\\\":{\\\"extendValue\\\":\\\"{\\\\\\\"province\\\\\\\":{\\\\\\\"n\n" +
                "ame\\\\\\\":\\\\\\\"北京\\\\\\\",\\\\\\\"id\\\\\\\":\\\\\\\"110000\\\\\\\"},\\\\\\\"city\\\\\\\":{\\\\\\\"name\\\\\\\":\\\\\\\"北京市\\\\\\\",\\\\\\\"id\\\\\\\":\\\\\\\"110100\\\\\\\"},\\\\\\\"street\\\\\\\":{\\\\\\\"name\\\\\\\":\\\\\\\"东华门街道\\\\\\\",\\\\\\\"id\\\\\\\":\\\\\\\"110101001\\\\\\\"},\\\\\\\"district\\\\\\\":{\\\\\\\"name\\\\\\\":\\\\\\\"东城区\\\\\\\",\\\\\\\"id\\\\\\\":\\\n" +
                "\\\\\"110101\\\\\\\"},\\\\\\\"detail\\\\\\\":{\\\\\\\"name\\\\\\\":\\\\\\\"无知名小路\\\\\\\"}}\\\",\\\"value\\\":\\\"北京,北京市,东城区,东华门街道,无知名小路\\\"},\\\"TextareaField-K55CWZ2E\\\":\\\"哈哈哈哈哈\\\",\\\"DDSelectField-K2U5GX3B\\\":{\\\"extendValue\\\":\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"网络销售\\\\\\\",\\\\\\\"key\\\\\\\n" +
                "\":\\\\\\\"option_K2U5LXIN\\\\\\\"}\\\",\\\"value\\\":\\\"网络销售\\\"},\\\"customer_follow_up_status\\\":{\\\"extendValue\\\":\\\"{\\\\\\\"extension\\\\\\\":{\\\\\\\"editFreeze\\\\\\\":true},\\\\\\\"label\\\\\\\":\\\\\\\"新获取\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"option_new_acquisition\\\\\\\"}\\\",\\\"value\\\":\\\"新获取\\\"},\\\"DDSelec\n" +
                "tField-K371T4RY\\\":{\\\"extendValue\\\":\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"潜在客户\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"option_0\\\\\\\"}\\\",\\\"value\\\":\\\"潜在客户\\\"},\\\"customer_name\\\":\\\"地址客户\\\",\\\"MultiTagField_94c9d4f0\\\":{\\\"extendValue\\\":\\\"[{\\\\\\\"key\\\\\\\":\\\\\\\"6df0b3ea93\\\\\\\"}]\\\",\\\"value\\\":\\\"[\\\n" +
                "\\\\\"供应商\\\\\\\"]\\\"},\\\"DDSelectField-K2U5GX39\\\":{\\\"extendValue\\\":\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"金融\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"option_K2U5KPJT\\\\\\\"}\\\",\\\"value\\\":\\\"金融\\\"},\\\"TextField_1DJNCI07S9OG0\\\":\\\"数据\\\",\\\"DDSelectField-K55CWZ2C\\\":{\\\"extendValue\\\":\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"\n" +
                "一般\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"option_0\\\\\\\"}\\\",\\\"value\\\":\\\"一般\\\"}},\\\"formCode\\\":\\\"PROC-CA5E9C3D-C96F-4E0E-9F33-7A4C2E9C3556\\\",\\\"extendData\\\":{\\\"biz_customer_last_status_change_dateV2\\\":\\\"1641982350873\\\",\\\"biz_customer_biz_scene\\\":\\\"crm_customer\\\"},\\\"permissi\n" +
                "on\\\":{\\\"participantStaffIds\\\":[],\\\"ownerStaffIds\\\":[\\\"manager7605\\\"]},\\\"gmtCreate\\\":1641982349000,\\\"orgId\\\":314091096,\\\"objectType\\\":\\\"crm_customer\\\",\\\"procInstStatus\\\":\\\"COMPLETED\\\",\\\"appUuid\\\":\\\"SWAPP-45C4B36964CB10B0DA7E216EFABF2E9E\\\",\\\"instanceId\\\":\n" +
                "\\\"e540e9b8-b373-45e2-ad0e-9c178b10ef8b\\\",\\\"procOutResult\\\":\\\"agree\\\"}],\\\"syncAction\\\":\\\"ding_paas_object_data_update\\\",\\\"success\\\":true,\\\"dingOpenErrcode\\\":0,\\\"syncSeq\\\":\\\"BAB1740953D85E5171AEBAA544\\\"}\",\"corp_id\":\"dingfeca3fa3352c7d4ca39a90f97fcb1e09\",\"\n" +
                "status\":0}]}";
        JSONObject jsonObject = JSONObject.parseObject(value);
        String value1 = jsonObject.toJSONString();
        log.info("");
    }

    @Test
    public void testCreateData() {



//        List<DingObjData> dingObjData = JSONArray.parseArray(JSONObject.toJSONString(dataMap), DingObjData.class);
        log.info("readVaule");

    }


    @Test
    public void testDesc() {
        String customerResult = dingManager.getCustomerDescribe("dingfeca3fa3352c7d4ca39a90f97fcb1e09", ConfigCenter.CRM_SUITE_ID);
        log.info("customer");
    }


    @Test
    public void testDesc1() {
        String customerResult = dingManager.getContactDescribe("dingfeca3fa3352c7d4ca39a90f97fcb1e09", ConfigCenter.CRM_SUITE_ID);
        log.info("customer");
    }

    @Test
    public void testAccount() {
        String customerResult = dingManager.queryCustomerDataById("dingfeca3fa3352c7d4ca39a90f97fcb1e09", ConfigCenter.CRM_SUITE_ID,"3bf62ea0-3731-4b3e-9693-5626c9d441e9");
        Map<String,Object> map= JsonPath.read(customerResult,"$.result_list[0]");
        DingBatchData dingObjData = JSONObject.parseObject(JSONObject.toJSONString(map), new TypeReference<DingBatchData>() {
        });
        Map dataMap = JSONObject.parseObject(dingObjData.getData(), Map.class);
        log.info("customer");
    }

    @Test
    public void testField() {

        DingObjTypeEnum crm_customer = DingObjTypeEnum.getEnumByDingApiName("crm_customer");

        List<Map<String, Object>> objFieldType = crm_customer.getObjFieldType();
        log.info("objFieldType");
    }

    @Test
    public void testOrder() {
        CreateCrmOrderArg createCrmOrderArg = new CreateCrmOrderArg();
        CreateCrmOrderArg.CrmOrderDetailInfo crmOrderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        crmOrderDetailInfo.setOrderId("*****************");
        crmOrderDetailInfo.setEnterpriseAccount("dtfxcs1485");

        crmOrderDetailInfo.setOrderTpye(2);
        crmOrderDetailInfo.setOrderTime(1641010255000L);
        createCrmOrderArg.setCrmOrderDetailInfo(crmOrderDetailInfo);
        CreateCrmOrderArg.CrmOrderProductInfo crmOrderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        crmOrderProductInfo.setProductId("61e7aef45d30b80001f50450");
        crmOrderProductInfo.setQuantity(1);
        crmOrderProductInfo.setAllResourceCount(1);
        //钉钉推送的金额是分

        crmOrderProductInfo.setOrderAmount("8000");
        Date fromDate = new Date(1641010255000L);
        Date endDate = new Date(1735704655000L);
        crmOrderProductInfo.setBeginTime(fromDate.getTime());
        crmOrderProductInfo.setEndTime(endDate.getTime());
        createCrmOrderArg.setCrmOrderProductInfo(crmOrderProductInfo);

        Result<String> crmOrder = crmManager.createCrmOrder(createCrmOrderArg);
        log.info("result");
    }

    @Test
    public void testInit() throws Exception {
        Result<String> stringResult = dataPushManager.initErpSetting("57255");
        log.info("stringResult");
    }

    @Test
    public void addEmpErp() {
        //查询钉钉的绑定的员工，插入erp员工表
        List<ErpEmployeeArg> erpEmployeeArgs = Lists.newArrayList();
        Integer ei = 82379;
        Result<List<DingMappingEmployeeResult>> allEmpResult = objectMappingService.batchGetEmpIds(ei, null, "");
        allEmpResult.getData().forEach(item -> {
            ErpEmployeeArg erpEmployeeArg = new ErpEmployeeArg();
            erpEmployeeArg.setErpEmployeeId(item.getDingEmployeeId());
            erpEmployeeArg.setErpEmployeeName(item.getDingEmployeeName());
            erpEmployeeArg.setFsEmployeeId(item.getEmployeeId());
            erpEmployeeArgs.add(erpEmployeeArg);
        });
        dataPushManager.batchSaveEmployeeData(String.valueOf(ei), erpEmployeeArgs);

    }

    @Test
    public void testData() {

        String model="{\n" +
                "        \"objAPIName\": \"crm_customer_personal\",\n" +
                "        \"masterFieldVal\": {\n" +
                "            \"creator_userid\": \"manager7605\",\n" +
                "            \"customer_id\": null,\n" +
                "            \"customer_name\": \"同步数据2113\",\n" +
                "            \"customer_follow_up_status\": \"option_new_acquisition\",\n" +
                "            \"address\": \"一个街道1111000\",\n" +
                "            \"customer_phone\": \"13532561396\",\n" +
                "            \"tel\": \"13532561396\",\n" +
                "            \"email\": null,\n" +
                "            \"remark\": \"同步数据2112\",\n" +
                "            \"object_describe_api_name\": \"crm_customer_personal\",\n" +
                "            \"tenant_id\": \"82379\"\n" +
                "        },\n" +
                "        \"detailFieldVals\": {}\n" +
                "    }";
        ErpPushDataObj erpPushDataObj = JSONObject.parseObject(model, new TypeReference<ErpPushDataObj>() {
        });
        Result<String> personCustomer = dataStorageService.createPersonCustomer(erpPushDataObj, 82379);

        String eventData = "{\n" +
                "\t\"result\": [{\n" +
                "\t\t\"creatorUserId\": \"manager7605\",\n" +
                "\t\t\"gmtModified\": 1643006625000,\n" +
                "\t\t\"data\": {\n" +
                "\t\t\t\"birthday\": 1642953600000,\n" +
                "\t\t\t\"address\": {\n" +
                "\t\t\t\t\"extendValue\": \"{\\\"province\\\":{\\\"name\\\":\\\"北京\\\",\\\"id\\\":\\\"110000\\\"},\\\"city\\\":{\\\"name\\\":\\\"北京市\\\",\\\"id\\\":\\\"110100\\\"},\\\"district\\\":{\\\"name\\\":\\\"东城区\\\",\\\"id\\\":\\\"110101\\\"},\\\"detail\\\":{\\\"name\\\":\\\"数据\\\"}}\",\n" +
                "\t\t\t\t\"value\": \"北京,北京市,东城区,数据\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"gender\": {\n" +
                "\t\t\t\t\"extendValue\": \"{\\\"label\\\":\\\"男\\\",\\\"key\\\":\\\"option_0\\\"}\",\n" +
                "\t\t\t\t\"value\": \"男\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"identity\": {\n" +
                "\t\t\t\t\"extendValue\": \"{\\\"label\\\":\\\"学生\\\",\\\"key\\\":\\\"option_0\\\"}\",\n" +
                "\t\t\t\t\"value\": \"学生\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"customer_phone\": {\n" +
                "\t\t\t\t\"extendValue\": \"{\\\"mode\\\":\\\"phone\\\",\\\"countryKey\\\":\\\"CN\\\",\\\"flag\\\":\\\"C\\\",\\\"countryCode\\\":\\\"+86\\\",\\\"areaNumber\\\":\\\"\\\",\\\"flagPy\\\":\\\"Z\\\",\\\"countryName\\\":\\\"China\\\",\\\"countryNameZh\\\":\\\"中国\\\",\\\"countryNamePy\\\":\\\"ZHONGGUO\\\"}\",\n" +
                "\t\t\t\t\"value\": \"13245226532\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"customer_follow_up_status\": {\n" +
                "\t\t\t\t\"extendValue\": \"{\\\"label\\\":\\\"待跟进\\\",\\\"key\\\":\\\"option_1\\\"}\",\n" +
                "\t\t\t\t\"value\": \"待跟进\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"MultiTagField-27c1d1f2\": {\n" +
                "\t\t\t\t\"extendValue\": \"[{\\\"key\\\":\\\"6df0b3ea92\\\"}]\",\n" +
                "\t\t\t\t\"value\": \"[\\\"中意向\\\"]\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"customer_name\": \"个人客户1024\"\n" +
                "\t\t},\n" +
                "\t\t\"formCode\": \"PROC-CC1CD57C-6ACB-4248-B539-B800256762B0\",\n" +
                "\t\t\"extendData\": {\n" +
                "\t\t\t\"biz_customer_last_status_change_dateV2\": \"1643006625257\",\n" +
                "\t\t\t\"biz_customer_biz_scene\": \"crm_customer_personal\"\n" +
                "\t\t},\n" +
                "\t\t\"permission\": {\n" +
                "\t\t\t\"participantStaffIds\": [],\n" +
                "\t\t\t\"ownerStaffIds\": [\"manager7605\"]\n" +
                "\t\t},\n" +
                "\t\t\"gmtCreate\": 1643006624000,\n" +
                "\t\t\"orgId\": 314091096,\n" +
                "\t\t\"objectType\": \"crm_customer_personal\",\n" +
                "\t\t\"procInstStatus\": \"COMPLETED\",\n" +
                "\t\t\"appUuid\": \"SWAPP-45C4B36964CB10B0DA7E216EFABF2E9E\",\n" +
                "\t\t\"instanceId\": \"5a0da382-0735-4fea-844b-313363e4e40d\",\n" +
                "\t\t\"procOutResult\": \"agree\"\n" +
                "\t}],\n" +
                "\t\"syncAction\": \"ding_paas_object_data_update\",\n" +
                "\t\"success\": true,\n" +
                "\t\"dingOpenErrcode\": 0,\n" +
                "\t\"syncSeq\": \"BA41740953DF5E55A1AEB4A544\"\n" +
                "}";
        Object object = JsonPath.read(eventData, "$.result[0]");
        DingObjData dingObjData = JSONObject.parseObject(JSONObject.toJSONString(object), new TypeReference<DingObjData>() {
        });
        dataPushManager.pushData(dingObjData, "dingfeca3fa3352c7d4ca39a90f97fcb1e09");
    }

    @Test
    public void testCase() {
        ComponentQueryArg componentQueryArg = new ComponentQueryArg();

        //TODO 换取成纷享的客户
        componentQueryArg.setAccountId("61e9394cc1b33c00011a5c35");

        componentQueryArg.setApiName("CasesObj");
        componentQueryArg.setFsUserId(1000);
        componentQueryArg.setOffset(0);
        componentQueryArg.setLimit(100);
        componentQueryArg.setEa("ddqybhzyl");
        componentQueryArg.setEi(82379);
        DingStorageResult<DingObjectDataListModel.Result> eserviceWorkData = dataStorageService.getEserviceWorkData(componentQueryArg);
    }

    @Test
    public void testRobotMessage() throws UnsupportedEncodingException {
        String data = "{\"Operator\":\"manager7605\",\"OpenConversationCorpId\":\"dingfeca3fa3352c7d4ca39a90f97fcb1e09\",\"OperateTime\":\"*************\",\"OpenConversationId\":\"cidIk3QHr4j7dc6rKTKu2JLZQ==\",\"syncAction\":\"im_cool_app_install\",\"CoolAppCode\":\"COOLAPP-1-101922FD2EEA210732B20000\",\"RobotCode\":\"oWGjPCWDy3PMsr116346312792161047\",\"syncSeq\":\"B456E6616DA3B27F3D2A3D9984\"}";
        ChatEventModel chatEventModel = JSONObject.parseObject(data, ChatEventModel.class);
        dingManager.robotSendCardMessage("dingfeca3fa3352c7d4ca39a90f97fcb1e09", "********", chatEventModel);
    }

    @Test
    public void testHistoryData(){
        Result<String> dingObjList = dingManager.getDingObjListNewVersion2("dingfeca3fa3352c7d4ca39a90f97fcb1e09", ConfigCenter.CRM_SUITE_ID, 1642655398000L, "0", 100L, "crm_customer");
        DingBatchResult dingBatchResult = JSONObject.parseObject(dingObjList.getData(), new TypeReference<DingBatchResult>() {
        });
        log.info("data");
    }

    @Test
    public void testPush(){
        String data=" {\n" +
                "            \"creator_userid\": \"0831443751903040249\",\n" +
                "            \"customer_id\": null,\n" +
                "            \"customer_name\": \"kkk\",\n" +
                "            \"customer_follow_up_status\": \"option_new_acquisition\",\n" +
                "            \"address\": \"地址121\",\n" +
                "            \"customer_phone\": \"13520614011\",\n" +
                "            \"tel\": \"13520614011\",\n" +
                "            \"email\": \"<EMAIL>\",\n" +
                "            \"remark\": \"我是需求1\",\n" +
                "            \"object_describe_api_name\": \"crm_customer_personal\",\n" +
                "            \"tenant_id\": \"82379\"\n" +
                "        }";
        Map erpPushDataObj = JSONObject.parseObject(data, new TypeReference<Map>() {
        });

        log.info("erp pudsh");
//        Map<String, Object> objectMap = dataConvertManager.convertData2Ding(erpPushDataObj.getMasterFieldVal(), erpPushDataObj.getObjAPIName());
        dingManager.createPersonalCustomer("dingfeca3fa3352c7d4ca39a90f97fcb1e09","********",erpPushDataObj,82379);
    }


    @Test
    public void testDataCreate(){
        DingObjSyncVo dingObjSyncVo=new DingObjSyncVo();
        dingObjSyncVo.setCrmApiName("LeadsObj");
        dingObjSyncVo.setDingApiName("crm_customer_personal");
        dingObjSyncVo.setCrmObjId("");
        dingObjSyncVo.setDingCorpId("1");
        dingObjSyncVo.setTenantId(1);
        dingObjSyncVo.setDingObjId("12111");
        Integer integer = dingObjSyncService.insertObjSync(dingObjSyncVo);

        log.info("sco");

    }

    @Test
    public void createShortUrl(){
        Result<String> shortUrl = crmManager.createShortUrl(82379, "https://www.ceshi112.com/hcrm/dingtalk/#/ava_fs_common/pages/webview/index?url=https%253A%252F%252Fwww.ceshi112.com%252Fhcrm%252Fdingtalk%252Ffunction%252Fava-bi-message%253Fid%253DBI_66bc4eb86e8540000190254b%2526diagram%253D1%2526from%253Dsubscription%2526dataType%253D2&redirect=1");
        System.out.println(shortUrl);
    }
}
