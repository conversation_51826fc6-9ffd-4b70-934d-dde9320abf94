package com.facishare.open.ding.provider.manager;

import base.BaseAbstractTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2025/5/13 16:52:15
 */
public class DingTodoManagerTest  extends BaseAbstractTest {
    @Autowired
    private DingTodoManager dingTodoManager;

    @Test
    public void updateStatus() {
        // 1920663745003044865
        final int i = dingTodoManager.updateStatus(0, 91226, "681d6518a5d82c0eecfb255b", 1000, "dingxodefmf8ogxkera3");
        System.out.println(i);
    }
}