package com.facishare.open.ding.common.utils;

import java.util.List;
import java.util.regex.Pattern;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>校验工具类</p>
 * @dateTime 2018/7/17 10:54
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public class ValidateUtil {

    /**
     * 正则表达式：验证邮箱
     */
    public static final String REGEX_EMAIL =
            "^(\\w)+([-+.]\\w+)*@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    /**
     * 正则表达式：验证URL
     */
    public static final String REGEX_URL = "(https?|ftp|file|fs)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";

    /**
     * 只能包含字母下划线中划线和数组
     */
    private static final String REGEX_LEGAL_STRING_ONE = "^[a-zA-Z0-9_-]+$";

    /**
     * 只能汉字，字母，数字，下划线，中划线组成
     */
    private static final String REGEX_LEGAL_STRING_TWO = "^[-a-zA-Z0-9_\\u4e00-\\u9fa5]+$";

    /**
     * appId前缀部分
     */
    private static final String APPID_PREFIX = "FSAID_";

    /**
     * corpId前缀部分
     */
    private static final String CORPID_PREFIX = "FSCID_";

    /**
     * OpenUserId前缀部分
     */
    private static final String OPENUSERID_PREFIX = "FSUID_";

    private static final int PREFIX_LENGTH = 6;

    private static final int KEY_LENGTH = 32;

    /**
     * 校验是否十六进制字符串
     */
    public static boolean isHex(String str) {
        char c;
        for (int i = 0; i < str.length(); i++) {
            c = str.charAt(i);
            if (!(((c >= '0') && (c <= '9')) || ((c >= 'A') && (c <= 'F')) || (c >= 'a') && (c <= 'f'))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证appId的格式是否正确
     */
    public static boolean isAppId(String appId) {
        return !StringUtils.isBlank(appId) && appId.startsWith(APPID_PREFIX);
    }

    /**
     * 验证appSecret
     * @param appSecret
     * @return
     */
    public static boolean isAppSecret(String appSecret) {
        return !StringUtils.isBlank(appSecret) && appSecret.length() == KEY_LENGTH && isHex(appSecret);
    }

    /**
     * 校验appAccessToken
     */
    public static boolean isAppAccessToken(String appAccessToken) {
        return !StringUtils.isBlank(appAccessToken) && appAccessToken.length() == KEY_LENGTH && isHex(appAccessToken);
    }

    /**
     * 校验permanentCode
     */
    public static boolean isPermanentCode(String permanentCode) {
        return !StringUtils.isBlank(permanentCode) && permanentCode.length() == KEY_LENGTH && isHex(permanentCode);
    }

    /**
     * 校验corpAccessToken
     */
    public static boolean isCorpAccessToken(String corpAccessToken) {
        return !StringUtils.isBlank(corpAccessToken) && corpAccessToken.length() == KEY_LENGTH && isHex(corpAccessToken);
    }

    /**
     * 校验corpId
     */
    public static boolean isCorpId(String corpId) {
        return !StringUtils.isBlank(corpId) && corpId.startsWith(CORPID_PREFIX) && isHex(corpId.substring(PREFIX_LENGTH));
    }

    /**
     * 校验openUserId
     */
    public static boolean isOpenUserId(String openUserId) {
        return !StringUtils.isBlank(openUserId) && openUserId.startsWith(OPENUSERID_PREFIX)
                && isHex(openUserId.substring(PREFIX_LENGTH));
    }

    /**
     * 校验openUserIds
     */
    public static boolean isOpenUserId(List<?> openUserIds) {
        if (openUserIds == null || openUserIds.isEmpty()) {
            return false;
        }
        for (Object openUserId : openUserIds) {
            if (!isOpenUserId(String.valueOf(openUserId))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验邮箱
     */
    public static boolean isEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return false;
        }
        return Pattern.matches(REGEX_EMAIL, email);
    }

    /**
     * 校验URL
     */
    public static boolean isUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        //return true;
        return Pattern.matches(REGEX_URL, url);
    }

    /**
     * 验证数组是否包含
     */
    public static boolean isArrayContain(Object param, Object... array) {
        if (param != null && array != null && ArrayUtils.contains(array, param)) {
            return true;
        }
        return false;
    }

    /**
     * 验证字符串是否由字母，数字，下划线，中划线组成
     * 
     * @param str
     * @return
     */
    public static boolean isLegalStringOne(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return Pattern.matches(REGEX_LEGAL_STRING_ONE, str);
    }

    /**
     * 验证字符串是否由汉字，字母，数字，下划线，中划线组成
     * 
     * @param str
     * @return
     */
    public static boolean isLegalStringTwo(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return Pattern.matches(REGEX_LEGAL_STRING_TWO, str);
    }

    /**
     * 验证手机号码
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile) {
        if (StringUtils.isBlank(mobile) || !StringUtils.isNumeric(mobile) || mobile.length() != 11) {
            return false;
        }
        return true;
    }

    public static void main(String args[]) {
        System.out.println(isEmail("<EMAIL>"));
        System.out.println(isUrl("http://fxiaoke.kingdiee.com/index.html"));
    }
}
