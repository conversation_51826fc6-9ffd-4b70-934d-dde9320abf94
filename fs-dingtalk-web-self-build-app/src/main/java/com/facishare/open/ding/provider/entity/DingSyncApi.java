package com.facishare.open.ding.provider.entity;

import lombok.Data;

import javax.persistence.Id;
import java.util.Date;

/**
 * <p>同步对象</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/12 14:23
 */
@Data
public class DingSyncApi {
    @Id
    private Long id;

    /** 纷享EI**/
    private Integer ei;

    /** 对象名称 **/
    private String apiName;

    /** 同步方向(1from纷享2to纷享) **/
    private Integer syncDirection;

    /** 最后同步时间 */
    private Long lastSyncTime;

    /** 创建时间 **/
    private Date createTime;

    /** 更新时间 **/
    private Date updateTime;

    private Integer version;

}
