package com.facishare.open.ding.api.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>同步日志Result</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/16 15:29
 */
@Data
public class KcSyncLogResult implements Serializable{
    private static final long serialVersionUID = 2053901426562266039L;

    private String id;

    /** 纷享EI**/
    private Integer ei;

    /** 同步任务ID **/
    private Long taskId;

    /** 同步方向(1from纷享2to纷享) **/
    private Integer syncDirection;

    /** 同步类型(1自动2手动) **/
    private Integer syncType;

    /** 对象名称 **/
    private String apiName;

    /** 数据ID **/
    private String dataId;

    /** 操作类型(新增/修改/作废/上架/下架) **/
    private Integer operationType;

    /** 操作状态(0同步失败1同步成功2部分成功) **/
    private Integer operationStatus;

    /** 操作详情 **/
    private String operationDetails;

    /** 同步总数 **/
    private Integer totalNum;

    /** 同步成功数 **/
    private Integer successNum;

    /** 同步失败数 **/
    private Integer failNum;

    /** 消息内容 **/
    private String content;

    private String request;

    private String response;

    /** 创建时间 **/
    private Date createTime;

    /** 更新时间 **/
    private Date updateTime;

    /** 创建人 **/
    private Integer createBy;

    /** 修改人 **/
    private Integer updateBy;

    /** 操作人 **/
    private String operation;

    /** 操作时间 **/
    private String operationTime;

    /** 对象名称 **/
    private String objName;

    /** 操作 **/
    private String operationName;

    /** 状态 **/
    private String statusName;
}
