package com.facishare.open.ding.template.message;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.ExternalOaTodoService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.CreateTodoContextArg;
import com.facishare.open.ding.provider.constants.OAMessageTag;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import com.facishare.open.ding.web.config.ConfigCenter;
import com.facishare.open.ding.web.utils.ObjectMapperUtils;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FunctionMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.function.FunctionMsgBase;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/12/12 15:20
 * @desc
 */
@Component
@Slf4j
public class DingSelfCreateTodoTemplate extends SendMsgHandlerTemplate {
    @Autowired
    private DingtalkManager dingtalkManager;
    @Autowired
    private ExternalOaTodoService externalOaTodoService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;


    @Override
    public void filterMsg(MethodContext context) {
        CreateTodoContextArg createTodoContextArg = context.getData();
        CreateTodoArg createTodoArg = createTodoContextArg.getCreateTodoArg();
        DingEnterpriseResult dingEnterpriseResult=createTodoContextArg.getDingEnterpriseResult();
            //TODO 多应用设计
        String tenantId=String.valueOf(createTodoArg.getEi());
        String functionName = ConfigCenter.functionMaps.get(tenantId);
        if(StringUtils.isBlank(functionName)){
            return;
        }

        FunctionMsgBase functionMsgBase=new FunctionMsgBase();
        functionMsgBase.setChannel(ChannelEnum.dingding.getEnumName());
        functionMsgBase.setFsEa(dingEnterpriseResult.getEa());
        functionMsgBase.setAppId(dingEnterpriseResult.getAppKey());
        functionMsgBase.setType(FunctionMsgTypeEnum.crmExternalMsgPush.getType());
        functionMsgBase.setOutEa(dingEnterpriseResult.getDingCorpId());
        functionMsgBase.setDataCenterId(outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, dingEnterpriseResult.getEa(), dingEnterpriseResult.getAppKey()));
        functionMsgBase.setEventType(OAMessageTag.CREATE_TO_DO_TAG);
        functionMsgBase.setData(JSONObject.toJSONString(createTodoArg));
        Map<String, Object> objectMap = ObjectMapperUtils.objectToMap(functionMsgBase);
        Result<List<DingTodoTypeEnum>> booleanResult = dingtalkManager.filterMsg(functionName, objectMap, createTodoArg.getEi());
        if(!booleanResult.isSuccess()){
            TemplateResult templateResult= TemplateResult.newErrorData(booleanResult.getData());
            context.setResult(templateResult);
            return;
        }
        if (CollectionUtils.isNotEmpty(booleanResult.getData())) {
            createTodoContextArg.setNeedSupportTypes(booleanResult.getData());
        }
    }

    @Override
    public void buildMsg(MethodContext context) {

    }

    @Override
    public void sendMsg(MethodContext context) {
        CreateTodoContextArg createTodoContextArg = context.getData();
        CreateTodoArg createTodoArg = createTodoContextArg.getCreateTodoArg();
        DingEnterpriseResult dingEnterpriseResult=createTodoContextArg.getDingEnterpriseResult();
        //将之前的判断，挪到这个方法里
        List<DingTodoTypeEnum> dingTodoTypeEnums=createTodoContextArg.getNeedSupportTypes();
        for (DingTodoTypeEnum dingTodoTypeEnum : dingTodoTypeEnums) {
            if(dingTodoTypeEnum.equals(DingTodoTypeEnum.DING_TODO)){
                dingtalkManager.createTodo(createTodoArg,dingEnterpriseResult);
            }else {
                externalOaTodoService.createTodo(createTodoArg, dingEnterpriseResult);
            }
        }
    }




}
