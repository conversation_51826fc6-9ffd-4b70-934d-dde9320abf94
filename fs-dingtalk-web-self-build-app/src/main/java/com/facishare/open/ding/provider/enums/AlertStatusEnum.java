package com.facishare.open.ding.provider.enums;

public enum AlertStatusEnum {
    TODO_STATUS(0, "推送待办"),
    TODO_AND_REMIND_STATUS(1, "推送待办和提醒"),
    REMIND_STATUS(2, "推送提醒"),
    CLOSED_STATUS(3, "关闭");

    private Integer status;
    private String msg;

    AlertStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMsg() {
        return msg;
    }
}
