package com.facishare.open.ding.provider.constants;

import com.google.common.collect.Lists;
import java.util.HashSet;
import java.util.List;

/**
 * @ClassName: Constant
 * @Description: Constant
 * @datetime 2019/3/11 16:13
 * @Version 1.0
 */
public class Constant {
    /**
     * 应用的SuiteKey，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String SUITE_KEY="dingc9bdc7e92b060e7135c2f4657eb6378f";

    /**
     * 回调URL加解密用。应用的数据加密密钥，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String ENCODING_AES_KEY = "twki1srsvwm2vaxecwyy2xxllc4r5t0zivt1qropp9s";

    /**
     * 回调URL签名用。应用的签名Token, 登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String TOKEN = "iMY3uFx9DPCc5S17i7Y";

    /**
     * 需要监听的事件类型
     * 目前监听员工修改和离职
     */
    public static List<String> CALL_BACK_TAG = Lists.newArrayList(
            "user_add_org","user_modify_org", "user_leave_org");
    //不使用回调地址
    public static final Integer IS_NOT_CALLBACK=1;
    //使用回调地址
    public static final Integer IS_CALLBACK=0;
    //成功
    public static final int errcode=0;
    public static final int SPECIAL_ERRCODE=30006;
    /**
     * 根级部门
     */
    public static Integer TREE_PARENT_ID = 999999;

    /**
     * 系统管理员
     */
    public static final Integer SYSTEM_MANAGER=-10000;

//    /**
//     * 系统管理员
//     */
//    public static final Integer SYSTEM_MANAGER=1000;

    /**
     * 模式一
     */
    public static final Integer SYNC_MODELONE=1;
    /**
     * role角色
     */
    public static final Integer COMMON=0;

    /**
     * 请求的部门不在范围内
     */
    public static final Integer SCOPE_OUT_DEPT=50004;

    /**
     * 获取姓名
     */
    public static final String FIELD_NAME="sys00-name";
    /**
     * 获取手机号
     */
    public static final String FIELD_MOBILE="sys00-mobile";
    /**
     * 获取部门列表
     */
    public static final String FIELD_DEPT="sys00-deptIds";
    /**
     * 获取主属部门
     */
    public static final String FIELD_MAIN_DEPT="sys00-mainDeptId";
    /**
     * success
     */
    public static final Integer SUCCESS_CODE=0;
    /**
     * 系统管理员
     */
    public static final Integer SYSTEM_USER=1000;
    /**
     * 根级部门
     */
    public static final Integer TREE_DEPT=999999;
}
