package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.api.service.ExternalMessageService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.config.ConfigCenter;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextCardMessageResult;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextMessageResult;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <p>外部接口</p>
 *
 * @version 1.0
 * @dateTime 2023-05-16 10:17
 * @anthor chenzx
 */
@Slf4j
@RestController
@RequestMapping("/external")
public class ExternalController extends BaseController {

    ThreadPoolExecutor executor = new ThreadPoolExecutor(50,200,60, TimeUnit.SECONDS,new LinkedBlockingQueue<>());


    @Autowired
    private ExternalMessageService externalMessageService;

    @RequestMapping(value = "/sendDingtalkMsg", method = RequestMethod.POST)
    public Result<String> sendDingtalkMsg(@RequestBody String msgStr, @RequestParam("typeVal") String typeVal) {
        if(StringUtils.isEmpty(msgStr) || StringUtils.isEmpty(typeVal)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if(ConfigCenter.SYNC_SEND_MSG) {
            executor.submit(()->{
                sendDingtalkMsg2(msgStr, typeVal);
            });

            log.info("ExternalController.sendDingtalkMsg,res={},executor={}", msgStr, executor.toString());
            return Result.newSuccess();
        } else {
            log.info("ExternalController.sendDingtalkMsg,sendDingtalkMsg2=true");
            return sendDingtalkMsg2(msgStr, typeVal);
        }
    }

    Result<String> sendDingtalkMsg2(@RequestBody String msgStr, @RequestParam("typeVal") String typeVal) {
        try {
            String returnMsg = null;
            switch (typeVal) {
                case "TextMsg":
                    SendTextMessageArg sendTextMessageArg = new Gson().fromJson(msgStr, new TypeToken<SendTextMessageArg>(){}.getType());
                    SendTextMessageResult sendTextMessageResult = externalMessageService.sendCommonTextMessage(sendTextMessageArg);
                    returnMsg = sendTextMessageResult.getMessage();
                    break;
                case "CardMsg":
                    SendTextCardMessageArg sendTextCardMessageArg = new Gson().fromJson(msgStr, new TypeToken<SendTextCardMessageArg>(){}.getType());
                    SendTextCardMessageResult sendTextCardMessageResult = externalMessageService.sendCommonTextCardMessage(sendTextCardMessageArg);
                    returnMsg = sendTextCardMessageResult.getMessage();
                    break;
//                case  "CreateTodo":
//                    CreateTodoArg createTodoArg = new Gson().fromJson(msgStr, new TypeToken<CreateTodoArg>(){}.getType());
//                    break;
                default:
                    return Result.newError(ResultCode.PARAMS_ERROR.getErrorCode(),"not supported message type: "+typeVal);
            }
            return Result.newSuccess(returnMsg);
        }catch (Exception e) {
            log.error("trace sendDingtalkMsg get exception, ", e);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
    }
}
