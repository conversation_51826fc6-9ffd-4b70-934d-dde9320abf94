package com.facishare.open.ding.common.model;

import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/3 10:05
 * @Version 1.0
 */
@Data
public class UserVo implements Serializable {
    private String unionid;
    private String userid;
    private String name;
    private String mobile;
    private String email;
    private String position;
    private String jobnumber;
    private List<Long> department;
    private Long mainDepartment;
    private String managerUserid;
    private Integer sexType;
    private String stateCode;
    /**
     * 入职时间
     * 信息面板中入职时间字段内有值才返回。
     * 第三方企业应用，不返回该参数。
     */
    private Long hiredDate;

    public DingTalkEmployeeObject convertByUserVo(UserVo userVo){
        DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
        employeeData.setStatus(0);
        employeeData.setUnionId(userVo.getUnionid());
        employeeData.setPosition(userVo.getPosition());
        employeeData.setJobNumber(userVo.getJobnumber());
        employeeData.setManager_userid(userVo.getManagerUserid());
        employeeData.setEmail(userVo.getEmail());
        employeeData.setSexType(String.valueOf(userVo.getSexType()));
        employeeData.setUserid(userVo.getUserid());
        employeeData.setName(userVo.getName());
        employeeData.setPhone(userVo.getMobile());
        employeeData.setDeptId(dingDeptId);
        employeeData.setDeptName(dingDeptName);
    }
}
