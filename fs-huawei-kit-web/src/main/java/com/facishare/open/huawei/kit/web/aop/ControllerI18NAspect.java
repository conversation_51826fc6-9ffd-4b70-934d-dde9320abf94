package com.facishare.open.huawei.kit.web.aop;

import com.facishare.open.huawei.kit.web.enums.UserContextSingleton;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-9-18
 * 国际化AOP
 */
public class ControllerI18NAspect {
    @Resource
    private I18NStringManager i18NStringManager;

    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        LogUtils.info("ControllerI18NAspect.around,begin");
        Object result = joinPoint.proceed();
        LogUtils.info("ControllerI18NAspect.around,result={}", result);

        //获取前端当前语言，只能使用这个key，不能从cookie里面获取，因为这个request的已经被CEP改变
        String lang = TraceUtils.getLocale();
        String tenantId = null;
        LogUtils.info("ControllerI18NAspect.around,userContext={}", UserContextSingleton.INSTANCE.getUserContext());
        if(UserContextSingleton.INSTANCE.getUserContext()!=null) {
            tenantId = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId()+"";
        }
        LogUtils.info("ControllerI18NAspect.around,lang={},tenantId={}", lang,tenantId);

        if (result != null) {
            if(result instanceof Result) {
                Result result2 = (Result) result;
                LogUtils.info("ControllerI18NAspect.around,result2={}", result2);
                if(result2!=null) {
                    if(StringUtils.isNotEmpty(result2.getI18nKey())) {
                        result2.setMsg(i18NStringManager.get2(result2.getI18nKey(), lang,tenantId, result2.getMsg(),result2.getI18nExtra()));
                        result2.setI18nKey(null);
                        result2.setI18nExtra(null);
                    }
                    LogUtils.info("ControllerI18NAspect.around,end,result2={}", result2);
                }
            }
        }
        LogUtils.info("ControllerI18NAspect.around,end,result={}", result);
        return result;
    }
}
