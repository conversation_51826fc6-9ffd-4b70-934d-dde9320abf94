package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.huawei.kit.web.info.AllOrgSyncInfo;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.huawei.kit.web.info.SingleOrgSyncInfo;
import com.facishare.open.huawei.kit.web.result.result.Result;

public interface HuaweiContactsService {
    /**
     * 同步人员
     */
    Result<Void> syncEmployee(AuthSyncInfo authSyncInfo);
    /**
     * 同步部门
     */
    Result<Void> syncDepartment(SingleOrgSyncInfo singleOrgSyncInfo);
    /**
     * 全量同步部门
     */
    Result<Void> syncAllDepartment(AllOrgSyncInfo allOrgSyncInfo);

    /**
     * 新增人员，不更新人员，返回失败状态
     */
    Result<Void> createEmployee(String tenantId, AuthSyncInfo.User user);
}
