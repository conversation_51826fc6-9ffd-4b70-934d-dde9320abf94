package com.facishare.open.huawei.kit.web.service.impl;

import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.huawei.kit.web.service.AppService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("appService")
public class AppServiceImpl implements AppService {
//    @Resource
//    private AppInfoManager appInfoManager;
//    @Resource
//    private OuterOaAppInfoManager outerOaAppInfoManager;
//
//    @Override
//    public Result<Integer> updateAppInfo(OuterOaAppInfoEntity entity) {
//        return new Result<>(appInfoManager.updateAppInfo(entity));
//    }
//
//    @Override
//    public Result<OuterOaAppInfoEntity> getAppInfo(String outEa) {
//        return new Result<>(appInfoManager.getEntity(outEa));
//    }
//
//    @Override
//    public Result<OuterOaAppInfoEntity> getAppInfo(String outEa, String appId) {
//        return new Result<>(appInfoManager.getEntity(outEa, appId));
//    }
}
