package com.facishare.open.huawei.kit.web.result.result;

import com.github.trace.TraceContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class Result<T> extends BaseResult implements Serializable {
    private static final long serialVersionUID = -1407891263555853715L;

    protected T data;

    public Result() {
        this.code = ResultCodeEnum.SUCCESS.getCode();
        this.msg = ResultCodeEnum.SUCCESS.getMsg();
        this.i18nKey = ResultCodeEnum.SUCCESS.getI18nKey();
    }

    public Result(T data) {
        this.code = ResultCodeEnum.SUCCESS.getCode();
        this.msg = ResultCodeEnum.SUCCESS.getMsg();
        this.i18nKey = ResultCodeEnum.SUCCESS.getI18nKey();
        this.data = data;
    }

    public Result(ResultCodeEnum resultCode) {
        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
        this.i18nKey = resultCode.getI18nKey();
    }

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <R> Result<R> newInstance(int code, String msg, R data) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        result.setTraceMsg(TraceContext.get().getTraceId());
        return result;
    }

    public static <R> Result<R> newInstanceByI18n(int code, String msg, R data, String i18nKey) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        result.setI18nKey(i18nKey);
        result.setTraceMsg(TraceContext.get().getTraceId());
        return result;
    }

    public static Result newInstance(int code, String msg) {
        return newInstance(code, msg,null);
    }

    public static Result newInstanceByI18n(int code, String msg, String i18nKey) {
        return newInstanceByI18n(code, msg,null, i18nKey);
    }

    private static <R> Result<R> newInstance(ResultCodeEnum resultCode, R data) {
        return newInstanceByI18n(resultCode.getCode(), resultCode.getMsg(), data, resultCode.getI18nKey());
    }

    public static <R> Result<R> newErrorByI18n(ResultCodeEnum resultCode) {
        return newInstanceByI18n(resultCode.getCode(), resultCode.getMsg(), resultCode.getI18nKey());
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode) {
        return newInstance(resultCode, null);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, String msg) {
        return newError(resultCode.getCode(), msg);
    }

    public static <R> Result<R> newErrorTrace(ResultCodeEnum resultCode, String traceMsg) {
        Result<R> result = new Result<>(resultCode);
        result.setTraceMsg(traceMsg);
        return result;
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, R data) {
        return newInstance(resultCode, data);
    }

    public static <R> Result<R> newError(int code, String msg) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }

    public static <R> Result<R> newError(int code, String msg, String traceMsg) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setTraceMsg(traceMsg);
        return result;
    }

    public static <R> Result<R> newErrorByI18n(int code, String msg, String traceMsg, String i18nKey) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setI18nKey(i18nKey);
        result.setTraceMsg(traceMsg);
        return result;
    }

    public static <R> Result<R> newSuccess() {
        return newInstance(ResultCodeEnum.SUCCESS, null);
    }

    public static <R> Result<R> newSuccess(R data) {
        return newInstance(ResultCodeEnum.SUCCESS, data);
    }

    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.getCode()==this.code;
    }


    /**
     * 复制错误结果，不复制数据
     *
     * @param result
     * @param <R>
     * @return
     */
    public static <R> Result<R> copy(BaseResult result) {
        return newInstance(result.getCode(), result.getMsg(), null);
    }

    @Override
    public String toString() {
        return "Result{" +
                "data=" + data +
                ", code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", traceMsg='" + traceMsg + '\'' +
                '}';
    }
}
