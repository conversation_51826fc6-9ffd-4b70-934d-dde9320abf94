package com.facishare.open.outer.oa.connector.web.excel.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号绑定Excel导入导出VO类
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeBindMappingVo {
    /**
     * 纷享员工ID
     */
    @ExcelProperty(value = "纷享员工ID", index = 0)
    private String fsEmpId;

    /**
     * OA系统员工ID
     */
    @ExcelProperty(value = "OA系统员工ID", index = 1)
    private String outEmpId;

    /**
     * 纷享员工姓名（仅导出时使用）
     */
    @ExcelProperty(value = "纷享员工姓名", index = 2)
    private String fsEmpName;

    /**
     * 纷享员工电话（仅导出时使用）
     */
    @ExcelProperty(value = "纷享员工电话", index = 3)
    private String fsEmpPhone;

    /**
     * 纷享员工部门（仅导出时使用）
     */
    @ExcelProperty(value = "纷享员工部门", index = 4)
    private String fsEmpDept;

    /**
     * OA系统员工姓名（仅导出时使用）
     */
    @ExcelProperty(value = "OA系统员工姓名", index = 5)
    private String outEmpName;

    /**
     * OA系统员工部门（仅导出时使用）
     */
    @ExcelProperty(value = "OA系统员工部门", index = 6)
    private String outEmpDept;

    /**
     * 绑定状态（仅导出时使用）
     */
    @ExcelIgnore
    private String bindStatus;
}