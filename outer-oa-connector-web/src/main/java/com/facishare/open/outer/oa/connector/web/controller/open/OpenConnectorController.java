package com.facishare.open.outer.oa.connector.web.controller.open;

import cn.hutool.core.bean.BeanUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.open.outer.oa.connector.web.config.ConfigCenter;
import com.facishare.open.outer.oa.connector.web.manager.OpenConnectorDataManager;
import com.facishare.open.outer.oa.connector.web.model.admin.*;

import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 开放连接器外部接口,转发只申请了：erpdss/open/oa/noAuth/*
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Slf4j
@RestController
@RequestMapping(value = "erpdss/open/oa/noAuth/standard")
public class OpenConnectorController {
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OpenConnectorManager openConnectorManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private SSOLoginService ssoLoginService;
    @Autowired
    private OpenConnectorDataManager openConnectorDataManager;
    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;

    private static final String VER = "V1_";

    /**
     * 免登录授权
     *
     * @param dcId
     * @param response
     * @param request
     * @throws IOException
     */
    @RequestMapping(value = "/loginAuth/{connectorApiName}/{dcId}", method = RequestMethod.GET)
    @ResponseBody
    public void loginAuth(@PathVariable("connectorApiName") String connectorApiName,
                          @PathVariable("dcId") String dcId,
                          HttpServletResponse response,
                          HttpServletRequest request) throws Exception {
        if (StringUtils.isBlank(connectorApiName)) {
            doFailedResponse(request, response, null, I18NStringEnum.s2.getI18nKey(), I18NStringEnum.s2.getI18nValue());
            return;
        }
        Result<OriginRequestArg> originRequestArgResult = getOriginRequestArg(request);
        if (!originRequestArgResult.isSuccess()) {
            doFailedResponse(request, response, null, originRequestArgResult.getI18nKey(), originRequestArgResult.getMsg());
            return;
        }
        OriginRequestArg originRequestArg = originRequestArgResult.getData();
        Result<OuterOaEnterpriseBindEntity> enterpriseBind = getOuterOaEnterpriseBindEntity(dcId);
        String tenantId = null;
        OuterOaEnterpriseBindEntity entity = null;
        if (enterpriseBind.isSuccess() && enterpriseBind.getData() != null) {
            entity = enterpriseBind.getData();
            tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(entity.getFsEa()));
            dcId = entity.getId();
        }
        Result<OuterIdentityMsg> loginAuthUrlMsg = openConnectorManager.getLoginAuthUrlMsg(tenantId, dcId, connectorApiName, originRequestArg);
        if (!loginAuthUrlMsg.isSuccess()) {
            doFailedResponse(request, response, null, loginAuthUrlMsg.getI18nKey(), loginAuthUrlMsg.getMsg());
            return;
        }
        if (loginAuthUrlMsg.getData() == null || StringUtils.isBlank(loginAuthUrlMsg.getData().getUserId())) {
            doFailedResponse(request, response, null, I18NStringEnum.s2.getI18nKey(), I18NStringEnum.s2.getI18nValue());
            return;
        }
        if (entity==null&&(StringUtils.isBlank(loginAuthUrlMsg.getData().getOutEa()) || StringUtils.isBlank(loginAuthUrlMsg.getData().getAppId()))) {
            doFailedResponse(request, response, null, I18NStringEnum.s2.getI18nKey(), I18NStringEnum.s2.getI18nValue());
            return;
        }
        loginAuthUrlMsg.getData().setOutEa(entity.getOutEa());
        loginAuthUrlMsg.getData().setAppId(entity.getAppId());
        List<OuterOaEnterpriseBindEntity> entities = null;
        String outEa = loginAuthUrlMsg.getData().getOutEa(), appId = loginAuthUrlMsg.getData().getAppId(), userId = loginAuthUrlMsg.getData().getUserId();
        if (entity == null) {
            OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(outEa).appId(appId).bindStatus(BindStatusEnum.normal).build();
            entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
            if (CollectionUtils.isEmpty(entities)) {
                doFailedResponse(request, response, tenantId, I18NStringEnum.s15.getI18nKey(), I18NStringEnum.s15.getI18nValue());
                return;
            } else {
                OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder().channel(ChannelEnum.standard_oa)
                        .outEa(outEa).appId(appId).outEmpId(userId).build();
                List<OuterOaEmployeeBindEntity> userBind = outerOaEmployeeBindManager
                        .getEntities(outerOaEmployeeBindParams);
                if (CollectionUtils.isEmpty(userBind)) {
                    doFailedResponse(request, response, tenantId, I18NStringEnum.s21.getI18nKey(), I18NStringEnum.s21.getI18nValue());
                    return;
                }
                List<String> dcIdList = userBind.stream().map(v -> v.getDcId()).collect(Collectors.toList());
                entities = entities.stream().filter(v -> dcIdList.contains(v.getId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(entities)) {
                    doFailedResponse(request, response, tenantId, I18NStringEnum.s21.getI18nKey(), I18NStringEnum.s21.getI18nValue());
                    return;
                }
            }
        } else {
            entities = Lists.newArrayList(entity);
        }
        if (entities.size() == 1) {
            entity = entities.get(0);
            OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder().channel(entity.getChannel()).dcId(entity.getId())
                    .outEa(entity.getOutEa()).appId(entity.getAppId()).outEmpId(userId).build();
            List<OuterOaEmployeeBindEntity> userBind = outerOaEmployeeBindManager
                    .getEntities(outerOaEmployeeBindParams);
            if (CollectionUtils.isEmpty(userBind)) {
                doFailedResponse(request, response, tenantId, I18NStringEnum.s21.getI18nKey(), I18NStringEnum.s21.getI18nValue());
                return;
            }
            OuterOaEmployeeBindEntity userBindEntity = userBind.get(0);
            if (tenantId == null) {
                tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(entity.getFsEa()));
            }
            CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(Integer.valueOf(tenantId), Integer.valueOf(userBindEntity.getFsEmpId())));
            CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
            log.info("ssoLogin token :{}", ssoResult);
            CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
            String fsToken = ssoResult.getToken();
            if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
                doFailedResponse(request, response, null, I18NStringEnum.s3.getI18nKey(), I18NStringEnum.s3.getI18nValue());
                return;
            }
            String redirectUrl = String.format(ConfigCenter.crm_domain + ConfigCenter.SSO_REDIRECT_URL, fsToken);
            response.sendRedirect(redirectUrl);
        } else {//暂时不支持，select_enterprise.html页面是根据channel来决定调用哪个接口的，需要处理
            response.sendRedirect(ConfigCenter.crm_domain + "/pc-login/build/select_enterprise.html?channel=standard&outEa=" + URLEncoder.encode(SecurityUtil.encryptStr(VER + outEa), "utf-8") + "&appId=" + URLEncoder.encode(SecurityUtil.encryptStr(VER + appId), "utf-8") + "&userId=" + URLEncoder.encode(SecurityUtil.encryptStr(VER + userId), "utf-8"));
        }

    }


    /**
     * auth授权接口
     *
     * @param
     * @param dcId
     * @param response
     * @param request
     * @throws IOException
     */
    @RequestMapping(value = "/doAuth/{connectorApiName}/{dcId}", method = RequestMethod.GET)
    @ResponseBody
    public void doAuth(@PathVariable("connectorApiName") String connectorApiName,
                       @PathVariable("dcId") String dcId,
                       HttpServletResponse response,
                       HttpServletRequest request) throws IOException {
        if (StringUtils.isBlank(connectorApiName)) {
            doFailedResponse(request, response, null, I18NStringEnum.s2.getI18nKey(), I18NStringEnum.s2.getI18nValue());
            return;
        }
        Result<OriginRequestArg> originRequestArgResult = getOriginRequestArg(request);
        if (!originRequestArgResult.isSuccess()) {
            doFailedResponse(request, response, null, originRequestArgResult.getI18nKey(), originRequestArgResult.getMsg());
            return;
        }
        OriginRequestArg originRequestArg = originRequestArgResult.getData();
        Result<OuterOaEnterpriseBindEntity> enterpriseBind = getOuterOaEnterpriseBindEntity(dcId);
        String tenantId = null;
        if (enterpriseBind.isSuccess() && enterpriseBind.getData() != null) {
            OuterOaEnterpriseBindEntity entity = enterpriseBind.getData();
            tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(entity.getFsEa()));
            dcId = entity.getId();
        }
        DoAuthArg doAuthArg = BeanUtil.copyProperties(originRequestArg, DoAuthArg.class);
        doAuthArg.setRedirectUrl(ConfigCenter.crm_domain + "/open/connector/loginAuth/" + connectorApiName + "/" + dcId);
        Result<OuterRedirectMsg> result = openConnectorManager.getDoAuthUrlMsg(tenantId, dcId, connectorApiName, doAuthArg);
        if (!result.isSuccess() || result.getData() == null || result.getData().getRedirectUrl() == null) {
            doFailedResponse(request, response, null, result.getI18nKey(), result.getMsg());
        }
        response.sendRedirect(result.getData().getRedirectUrl());
    }


    /**
     * 事件回调
     *
     * @param dcId
     * @param response
     * @param request
     * @return
     */
    @RequestMapping(value = "/push/{connectorApiName}/{dcId}", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> push(@PathVariable("connectorApiName") String connectorApiName,
                             @PathVariable("dcId") String dcId,
                             HttpServletResponse response,
                             HttpServletRequest request) {
        if (StringUtils.isBlank(connectorApiName) || StringUtils.isBlank(dcId)) {
            doFailedResponse(request, response, null, I18NStringEnum.s2.getI18nKey(), I18NStringEnum.s2.getI18nValue());
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Result<OriginRequestArg> originRequestArgResult = getOriginRequestArg(request);
        if (!originRequestArgResult.isSuccess()) {
            return Result.copy(originRequestArgResult);
        }
        OriginRequestArg originRequestArg = originRequestArgResult.getData();
        Result<OuterOaEnterpriseBindEntity> enterpriseBind = getOuterOaEnterpriseBindEntity(dcId);
        String tenantId = null;
        if (enterpriseBind.isSuccess() && enterpriseBind.getData() != null) {
            OuterOaEnterpriseBindEntity entity = enterpriseBind.getData();
            tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(entity.getFsEa()));
            dcId = entity.getId();
        }
        Result<OuterCallBackEventMsg> result = openConnectorManager.dealWithCallBackEvent(tenantId, dcId, connectorApiName, originRequestArg);
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        return openConnectorDataManager.dealWithCallBackEvent(tenantId, enterpriseBind.getData(), result.getData());
    }

    private void doFailedResponse(HttpServletRequest request, HttpServletResponse response, String tenantId, String i18nKey, String defaultMsg) {
        try {
            response.setContentType("application/json;charset=UTF-8");
            if (i18nKey == null) {
                response.getWriter().write(defaultMsg);
            } else {
                String lang = this.getDefaultLang(request, tenantId);
                if (lang == null) {
                    response.getWriter().write(defaultMsg);
                } else {
                    String msg = I18nClient.getInstance().get(i18nKey, 0, lang, defaultMsg);
                    response.getWriter().write(msg);
                }
            }
        } catch (IOException e) {
            log.error("doFailedResponse error", e);
        }
    }

    private Result<OuterOaEnterpriseBindEntity> getOuterOaEnterpriseBindEntity(String dcId) {
        if (StringUtils.isBlank(dcId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntityById(dcId);
        if (entity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return Result.newSuccess(entity);

    }

    private Result<OriginRequestArg> getOriginRequestArg(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> headers = Maps.newHashMap();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }
        Map<String, String[]> parameterMap = request.getParameterMap();
        String body = null;
        try {
            if ("POST".equalsIgnoreCase(request.getMethod())) {
                body = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            }
        } catch (Exception e) {
            log.warn("get body Exception e=", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
        OriginRequestArg arg = new OriginRequestArg();
        arg.setBody(body);
        arg.setParamerMap(parameterMap);
        arg.setHeaders(headers);
        return Result.newSuccess(arg);
    }

    private String getDefaultLang(HttpServletRequest request, String tenantId) {
        String locale = TraceContext.get().getLocale();
        if (locale == null) {
            Locale requestLocale = request.getLocale();
            if (requestLocale != null) {
                locale = requestLocale.getDisplayLanguage();//不知道有没有用
            } else if (StringUtils.isNotBlank(tenantId)) {
                locale = i18NStringManager.getDefaultLang(tenantId);
            } else {
                locale = I18NStringManager.DEFAULT_LANG;
            }
        }
        return locale;
    }
}
