package com.facishare.open.outer.oa.connector.web.model.bind;

import lombok.Data;
import java.util.Map;

/**
 * 字段映射配置
 */
@Data
public class FieldMappingConfig {
    /**
     * 部门字段映射 key: 外部系统字段 value: CRM字段
     */
    private Map<String, String> departmentMapping;

    /**
     * 人员字段映射 key: 外部系统字段 value: CRM字段
     */
    private Map<String, String> employeeMapping;

    /**
     * 默认生成的CRM人员状态
     */
    private String defaultEmployeeStatus;
}